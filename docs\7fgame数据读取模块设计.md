# 7fgame数据读取模块设计

## 1. 模块概述

本模块专门用于从7fgame游戏文件夹中读取自动化所需的数据，严格遵循只读原则，不对游戏文件进行任何修改。

## 2. 核心类设计

### 2.1 GameDataReader 主类

```python
class GameDataReader:
    """7fgame游戏数据读取器"""
    
    def __init__(self, game_path: str):
        self.game_path = game_path
        self.cache = {}
        self.last_update = {}
        
    def get_login_status(self) -> dict
    def get_account_list(self) -> list
    def get_zhangong_tasks(self) -> dict
    def get_hero_info(self) -> dict
    def get_game_state(self) -> dict
```

### 2.2 专用数据读取器

#### AccountDataReader - 账号数据读取器
```python
class AccountDataReader:
    """账号相关数据读取"""
    
    def read_user_data(self) -> dict
    def get_account_folders(self) -> list
    def parse_account_info(self, account_id: str) -> dict
```

#### TaskDataReader - 任务数据读取器
```python
class TaskDataReader:
    """战功任务数据读取"""
    
    def read_zhangong_json(self) -> dict
    def parse_task_requirements(self) -> list
    def identify_task_type(self, task: dict) -> str
    def get_required_heroes(self, task: dict) -> list
```

#### HeroDataReader - 英雄数据读取器
```python
class HeroDataReader:
    """英雄信息数据读取"""
    
    def read_hero_info_json(self) -> dict
    def get_hero_by_country(self, country: str) -> list
    def get_hero_skills(self, hero_id: str) -> dict
    def match_task_heroes(self, task_type: str) -> list
```

#### GameStateReader - 游戏状态读取器
```python
class GameStateReader:
    """游戏运行状态读取"""
    
    def read_game_state_dat(self) -> dict
    def check_game_running(self) -> bool
    def get_current_room_info(self) -> dict
    def read_latest_game_log(self) -> dict
```

## 3. 数据文件映射

### 3.1 关键文件路径常量

```python
class GamePaths:
    """游戏文件路径常量"""
    
    # 用户数据
    USER_DATA = "7FGameUser.dat"
    ACCOUNTS_DIR = "Accounts"
    
    # 任务数据
    ZHANGONG_JSON = "Data/zhangong.json"
    HERO_INFO_JSON = "Data/QXMyHeroInfo.json"
    
    # 状态数据
    GAME_STATE = "sdata/game_state_1.dat"
    SERVER_CONFIG = "Server.ini"
    CLIENT_CONFIG = "ClientType.ini"
    
    # 日志数据
    GAME_LOG_DIR = "GameLog"
    SYSTEM_LOG_DIR = "Log"
    
    # 资源数据
    USER_ICON_DIR = "UserIcon"
    GAME_BAG_DIR = "GameBag"
```

### 3.2 数据结构定义

```python
@dataclass
class LoginStatus:
    """登录状态数据结构"""
    is_logged_in: bool
    username: str
    login_time: datetime
    server_info: dict

@dataclass
class ZhangongTask:
    """战功任务数据结构"""
    task_id: str
    task_type: str  # "hero", "country", "any"
    required_hero: str
    required_country: str
    task_description: str
    progress: int
    target: int
    is_completed: bool

@dataclass
class HeroInfo:
    """英雄信息数据结构"""
    hero_id: str
    hero_name: str
    country: str
    skills: list
    icon_path: str
```

## 4. 数据读取策略

### 4.1 文件访问策略

```python
class FileAccessStrategy:
    """文件访问策略"""
    
    @staticmethod
    def safe_read_file(file_path: str, mode: str = 'r') -> any:
        """安全读取文件，处理各种异常情况"""
        try:
            with open(file_path, mode, encoding='utf-8') as f:
                return f.read()
        except (FileNotFoundError, PermissionError, UnicodeDecodeError) as e:
            logger.warning(f"读取文件失败: {file_path}, 错误: {e}")
            return None
    
    @staticmethod
    def read_json_file(file_path: str) -> dict:
        """读取JSON文件"""
        
    @staticmethod
    def read_ini_file(file_path: str) -> configparser.ConfigParser:
        """读取INI文件"""
        
    @staticmethod
    def read_binary_file(file_path: str) -> bytes:
        """读取二进制文件"""
```

### 4.2 缓存机制

```python
class DataCache:
    """数据缓存管理"""
    
    def __init__(self, cache_timeout: int = 300):
        self.cache = {}
        self.cache_timeout = cache_timeout
        self.last_update = {}
    
    def get_cached_data(self, key: str) -> any:
        """获取缓存数据"""
        
    def set_cached_data(self, key: str, data: any) -> None:
        """设置缓存数据"""
        
    def is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
```

## 5. 实际应用场景

### 5.1 自动化流程中的数据读取

```python
def automation_workflow():
    """自动化工作流程中的数据读取示例"""
    
    reader = GameDataReader("path/to/7fgame")
    
    # 1. 检查登录状态
    login_status = reader.get_login_status()
    if not login_status['is_logged_in']:
        return "需要先登录"
    
    # 2. 读取战功任务
    tasks = reader.get_zhangong_tasks()
    current_task = find_current_task(tasks)
    
    # 3. 根据任务选择英雄
    hero_info = reader.get_hero_info()
    suitable_heroes = match_heroes_for_task(current_task, hero_info)
    
    # 4. 检查游戏状态
    game_state = reader.get_game_state()
    if game_state['in_game']:
        return "游戏正在进行中"
    
    return {
        'task': current_task,
        'heroes': suitable_heroes,
        'ready_to_start': True
    }
```

### 5.2 任务识别逻辑

```python
def identify_task_requirements(task_data: dict) -> dict:
    """识别任务需求"""
    
    task_type = "unknown"
    required_heroes = []
    
    # 英雄任务识别
    hero_names = ["华佗", "刘备", "诸葛瑾", "陆逊", "孙权", "曹操"]
    for hero in hero_names:
        if hero in task_data.get('description', ''):
            task_type = "hero"
            required_heroes.append(hero)
    
    # 国家英雄任务识别
    countries = ["蜀国", "魏国", "吴国"]
    for country in countries:
        if country in task_data.get('description', ''):
            task_type = "country"
            required_heroes.extend(get_heroes_by_country(country))
    
    # 任意英雄任务
    if "任意英雄" in task_data.get('description', ''):
        task_type = "any"
        required_heroes = get_all_heroes()
    
    return {
        'type': task_type,
        'heroes': required_heroes,
        'description': task_data.get('description', '')
    }
```

## 6. 错误处理和日志

### 6.1 异常处理策略

```python
class GameDataException(Exception):
    """游戏数据读取异常"""
    pass

def handle_file_access_error(func):
    """文件访问错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except FileNotFoundError:
            logger.error(f"文件不存在: {func.__name__}")
            return None
        except PermissionError:
            logger.error(f"文件访问权限不足: {func.__name__}")
            return None
        except Exception as e:
            logger.error(f"读取数据时发生未知错误: {e}")
            return None
    return wrapper
```

## 7. 测试和验证

### 7.1 单元测试设计

```python
class TestGameDataReader(unittest.TestCase):
    """游戏数据读取器测试"""
    
    def setUp(self):
        self.test_game_path = "test_data/7fgame"
        self.reader = GameDataReader(self.test_game_path)
    
    def test_read_login_status(self):
        """测试登录状态读取"""
        
    def test_read_zhangong_tasks(self):
        """测试战功任务读取"""
        
    def test_hero_info_parsing(self):
        """测试英雄信息解析"""
```

## 8. 性能优化

### 8.1 读取优化策略

- 使用文件监控避免不必要的重复读取
- 实现智能缓存机制
- 异步读取大文件
- 批量处理多个小文件

### 8.2 内存管理

- 及时释放大文件内容
- 使用生成器处理大量数据
- 限制缓存大小避免内存泄漏
