#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理模块

开发者: @ConceptualGod
"""

import pandas as pd
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

class AccountManager:
    """账号管理器"""
    
    def __init__(self, account_file: str = "账号表.xlsx"):
        """
        初始化账号管理器
        
        Args:
            account_file: 账号表文件路径
        """
        self.account_file = Path(account_file)
        self.logger = logging.getLogger(__name__)
        self.accounts = []
        
        self._load_accounts()
    
    def _load_accounts(self):
        """加载账号信息"""
        try:
            if not self.account_file.exists():
                self.logger.error(f"账号表文件不存在: {self.account_file}")
                return
            
            # 读取Excel文件
            df = pd.read_excel(self.account_file)
            
            # 转换为字典列表
            self.accounts = df.to_dict('records')
            
            # 过滤掉空行
            self.accounts = [acc for acc in self.accounts if pd.notna(acc.get('username', ''))]
            
            self.logger.info(f"成功加载 {len(self.accounts)} 个账号 - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"加载账号表异常: {str(e)}")
            self.accounts = []
    
    def get_accounts(self) -> List[Dict[str, Any]]:
        """
        获取所有账号
        
        Returns:
            账号列表
        """
        return self.accounts
    
    def get_account_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """
        根据用户名获取账号
        
        Args:
            username: 用户名
            
        Returns:
            账号信息字典
        """
        for account in self.accounts:
            if account.get('username') == username:
                return account
        return None
    
    def get_active_accounts(self) -> List[Dict[str, Any]]:
        """
        获取激活的账号
        
        Returns:
            激活账号列表
        """
        return [acc for acc in self.accounts 
                if acc.get('status', '').lower() in ['active', '激活', '启用', '1', 'true']]
    
    def update_account_status(self, username: str, status: str):
        """
        更新账号状态
        
        Args:
            username: 用户名
            status: 新状态
        """
        for account in self.accounts:
            if account.get('username') == username:
                account['status'] = status
                self.logger.info(f"更新账号 {username} 状态为: {status}")
                break
    
    def save_accounts(self):
        """保存账号信息到Excel文件"""
        try:
            if not self.accounts:
                self.logger.warning("没有账号数据需要保存")
                return
            
            df = pd.DataFrame(self.accounts)
            df.to_excel(self.account_file, index=False)
            
            self.logger.info(f"账号信息已保存到: {self.account_file}")
            
        except Exception as e:
            self.logger.error(f"保存账号信息异常: {str(e)}")
    
    def add_account(self, username: str, password: str, **kwargs):
        """
        添加新账号
        
        Args:
            username: 用户名
            password: 密码
            **kwargs: 其他账号信息
        """
        account = {
            'username': username,
            'password': password,
            'status': 'active',
            **kwargs
        }
        
        self.accounts.append(account)
        self.logger.info(f"添加新账号: {username}")
    
    def remove_account(self, username: str):
        """
        删除账号
        
        Args:
            username: 用户名
        """
        self.accounts = [acc for acc in self.accounts 
                        if acc.get('username') != username]
        self.logger.info(f"删除账号: {username}")
    
    def get_account_count(self) -> int:
        """获取账号总数"""
        return len(self.accounts)
    
    def get_active_account_count(self) -> int:
        """获取激活账号数量"""
        return len(self.get_active_accounts())
