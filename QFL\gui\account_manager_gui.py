#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理GUI模块

开发者: @ConceptualGod
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import logging
from typing import List, Dict, Any, Optional
from .file_handler import FileHandler

class AccountManagerGUI:
    """账号管理GUI"""
    
    def __init__(self, parent_frame):
        """
        初始化账号管理GUI
        
        Args:
            parent_frame: 父级框架
        """
        self.parent_frame = parent_frame
        self.logger = logging.getLogger(__name__)
        self.file_handler = FileHandler()
        
        # 账号数据
        self.accounts = []
        
        # 创建界面
        self._create_widgets()
        
        # 加载账号数据
        self._load_accounts()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮
        ttk.Button(button_frame, text="导入文件", command=self._import_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出文件", command=self._export_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="添加账号", command=self._add_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="编辑账号", command=self._edit_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除账号", command=self._delete_account).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新列表", command=self._refresh_list).pack(side=tk.LEFT, padx=(0, 5))
        
        # 账号列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('username', 'password')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        self.tree.heading('username', text='用户名')
        self.tree.heading('password', text='密码')

        # 设置列宽
        self.tree.column('username', width=200)
        self.tree.column('password', width=200)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', self._on_double_click)
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)

        # 署名水印
        signature_label = ttk.Label(status_frame, text="By @ConceptualGod",
                                   foreground="gray", font=("Arial", 8))
        signature_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.count_label = ttk.Label(status_frame, text="账号数量: 0")
        self.count_label.pack(side=tk.RIGHT)
    
    def _load_accounts(self):
        """加载账号数据"""
        try:
            self.accounts = self.file_handler.load_accounts_from_json()
            self._update_tree()
            self._update_status(f"加载了 {len(self.accounts)} 个账号")

        except Exception as e:
            self.logger.error(f"加载账号数据异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"加载账号数据失败: {str(e)}")
    
    def _update_tree(self):
        """更新账号列表显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加账号数据
        for i, account in enumerate(self.accounts):
            # 隐藏密码显示
            display_password = '*' * len(account.get('password', ''))

            self.tree.insert('', 'end', values=(
                account.get('username', ''),
                display_password
            ))
        
        # 更新计数
        self.count_label.config(text=f"账号数量: {len(self.accounts)}")
    
    def _import_file(self):
        """导入文件"""
        try:
            accounts = self.file_handler.import_file()
            if accounts:
                self.accounts = accounts
                self._update_tree()
                self._update_status(f"成功导入 {len(accounts)} 个账号")
            
        except Exception as e:
            self.logger.error(f"导入文件异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"导入文件失败: {str(e)}")

    def _export_file(self):
        """导出文件"""
        try:
            if not self.accounts:
                messagebox.showwarning("警告 - By @ConceptualGod", "没有账号数据可导出")
                return

            self.file_handler.export_accounts(self.accounts)
            self._update_status("账号数据导出完成")

        except Exception as e:
            self.logger.error(f"导出文件异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"导出文件失败: {str(e)}")
    
    def _add_account(self):
        """添加账号"""
        dialog = AccountEditDialog(self.parent_frame, "添加账号")
        if dialog.result:
            # 检查账号是否已存在
            if self.file_handler.check_account_exists(dialog.result['username']):
                messagebox.showwarning("警告 - By @ConceptualGod", f"账号 {dialog.result['username']} 已存在")
                return

            self.accounts.append(dialog.result)
            self._save_and_refresh()
    
    def _edit_account(self):
        """编辑账号"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告 - By @ConceptualGod", "请选择要编辑的账号")
            return
        
        # 获取选中的账号索引
        item = selected[0]
        index = self.tree.index(item)
        
        if 0 <= index < len(self.accounts):
            account = self.accounts[index]
            dialog = AccountEditDialog(self.parent_frame, "编辑账号", account)
            if dialog.result:
                self.accounts[index] = dialog.result
                self._save_and_refresh()
    
    def _delete_account(self):
        """删除账号"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告 - By @ConceptualGod", "请选择要删除的账号")
            return

        # 确认删除
        if messagebox.askyesno("确认 - By @ConceptualGod", "确定要删除选中的账号吗？"):
            # 获取选中的账号索引
            item = selected[0]
            index = self.tree.index(item)
            
            if 0 <= index < len(self.accounts):
                del self.accounts[index]
                self._save_and_refresh()
    
    def _refresh_list(self):
        """刷新列表"""
        self._load_accounts()
    
    def _on_double_click(self, event):
        """双击事件处理"""
        self._edit_account()
    
    def _save_and_refresh(self):
        """保存并刷新"""
        try:
            self.file_handler.save_accounts(self.accounts)
            self._update_tree()
            self._update_status("账号数据已保存")
            
        except Exception as e:
            self.logger.error(f"保存账号数据异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"保存账号数据失败: {str(e)}")
    
    def _update_status(self, message: str):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.logger.info(message)
    
    def get_accounts(self) -> List[Dict[str, Any]]:
        """获取账号列表"""
        return self.accounts


class AccountEditDialog:
    """账号编辑对话框"""
    
    def __init__(self, parent, title: str, account: Optional[Dict[str, Any]] = None):
        """
        初始化对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            account: 要编辑的账号数据
        """
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"{title} - By @ConceptualGod")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
        
        # 创建表单
        self._create_form(account)
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _create_form(self, account: Optional[Dict[str, Any]]):
        """创建表单"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 表单字段
        fields = [
            ('用户名', 'username'),
            ('密码', 'password')
        ]
        
        self.entries = {}
        
        for i, (label, key) in enumerate(fields):
            ttk.Label(main_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=5)

            # 普通输入框
            entry = ttk.Entry(main_frame, width=30)
            entry.grid(row=i, column=1, sticky=tk.EW, padx=(10, 0), pady=5)
            if account and account.get(key):
                entry.insert(0, account.get(key, ''))
            self.entries[key] = entry

            # 密码框隐藏显示
            if key == 'password':
                entry.config(show='*')
        
        # 配置列权重
        main_frame.columnconfigure(1, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self._ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self._cancel_clicked).pack(side=tk.LEFT)
    
    def _ok_clicked(self):
        """确定按钮点击"""
        try:
            # 获取表单数据
            username = self.entries['username'].get().strip()
            password = self.entries['password'].get().strip()
            
            if not username or not password:
                messagebox.showerror("错误 - By @ConceptualGod", "用户名和密码不能为空")
                return

            self.result = {
                'username': username,
                'password': password
            }
            
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误 - By @ConceptualGod", f"保存数据失败: {str(e)}")
    
    def _cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()
