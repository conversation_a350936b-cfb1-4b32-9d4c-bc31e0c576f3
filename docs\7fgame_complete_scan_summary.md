[模式:研究][模型:Claude Sonnet 4]

# 7fgame文件夹完整扫描总结

## 概述
本文档是对7fgame文件夹的完整扫描分析，旨在为起凡游戏自动化系统提供全面的技术参考。扫描发现了游戏客户端的完整结构，包括配置文件、日志系统、账户数据、游戏数据和插件系统。

## 目录结构分析

### 1. 根目录文件
- **7fGame.exe** - 主游戏客户端程序
- **7fxp.dll** - 游戏核心动态链接库
- **game.exe** - 游戏执行程序
- **gpigame.dll** - 游戏平台接口库
- **Server.ini** - 服务器配置文件

### 2. 核心配置目录

#### 2.1 Data目录 - 游戏数据配置
- **zhangong.json** - 战功任务定义文件
  - 包含任务ID、描述、数值和标志位
  - 示例：{"Id":1,"Desc":"获得1局胜利","Value":1,"Flag":3}
  - 包含治疗英雄列表：cure_heros: "23,26,57,150,261,330,186,188,268"

- **QXMyHeroInfo.json** - 群雄逐鹿英雄信息
  - 完整的英雄数据库，包含技能、装备、觉醒能力
  - 英雄分类、技能描述、游戏机制
  - 皮肤数据、装备配置、战斗统计

- **gamestart_check_file_list.txt** - 游戏启动检查文件列表
  - 包含：7fxp.dll, game.exe, gpigame.dll等核心文件

#### 2.2 Server.ini - 服务器配置
```ini
服务器IP地址：
- **************
- **************  
- ***************
```

### 3. 账户系统

#### 3.1 Accounts目录 - 用户账户数据
发现5个账户文件夹：
- ************* - 账户ID
- ************ - 账户ID
- ************ - 账户ID
- ************ - 账户ID
- ************ - 账户ID

每个账户包含：
- **UserDB.db** - 用户数据库
- **account.db** - 账户信息
- **chat.db** - 聊天记录
- **unionchat.db** - 联盟聊天
- **Settings.ini** - 个人设置
- **GameSet.ini** - 游戏设置
- **Flag.ini** - 标志配置

### 4. 日志系统

#### 4.1 Log目录 - 系统日志
按时间戳组织的日志文件夹：
- ******************
- ****************** 
- ******************
- ******************
- ******************

每个时间戳文件夹包含：
- **7fGame.log** - 主游戏日志
- **7fHomePage.log** - 主页日志
- **ProcManager.log** - 进程管理日志
- **QfGameMessageImpl.log** - 游戏消息实现日志

#### 4.2 GameLog目录 - 游戏内日志
游戏会话日志，包含详细的游戏状态信息：
- **log2244-2025.07.22-04.30.23** - 游戏会话日志
- **log5704-2025.07.24-05.07.22** - 游戏会话日志
- **log7844-2025.07.24-04.57.18** - 游戏会话日志

每个会话包含：
- **net_state.log** - 网络状态日志（重要：包含服务器连接信息）
- **cmd.log** - 命令日志
- **error.log** - 错误日志
- **mission.log** - 任务日志
- **replay*.7fr** - 游戏回放文件

### 5. 插件系统

#### 5.1 plugin目录
- **7FHomepage9Plus** - 主页插件（新版本）
- **7fHomepage** - 主页插件（旧版本）
- **UserHeaderUpload** - 用户头像上传插件

### 6. 关键发现

#### 6.1 服务器通信信息
从net_state.log中发现的实际服务器连接：
```
HostSrvIP: ***************
HostSrvPort: 2175
SessionName: auto
UserName: k****
Pos: 18
UserId: ********
MapName: sanguo_b.map
ReConnectSrvIP: **************:7500
```

#### 6.2 游戏状态数据
- **MapID**: 47 (地图ID)
- **RoomID**: 4319 (房间ID)
- **PlatLev**: 8 (平台等级)
- **TeamId**: 1 (队伍ID)

#### 6.3 网络通信模式
- 使用管道通信：`\\.\pipe\GAME_XQYFZ_CHS_123456`
- HTTP API调用：`http://rvs.7fgame.com/api/v1/sysinfo`
- 版本检查：`http://pt.7fgame.com/platformmanage/platforminterface/gamestart_check_file_list.txt`

## 自动化系统设计建议

### 1. 数据读取策略
基于扫描结果，自动化系统应该：

#### 1.1 实时数据源
- **网络数据包监听** - 监听与服务器的通信
- **内存数据读取** - 读取游戏进程内存中的实时状态
- **日志文件监控** - 实时监控GameLog中的状态变化

#### 1.2 配置数据源
- **zhangong.json** - 获取战功任务定义
- **QXMyHeroInfo.json** - 获取英雄技能和属性信息
- **账户配置文件** - 读取用户设置和游戏配置

### 2. 关键监控点
- **net_state.log** - 监控网络连接状态和游戏会话信息
- **mission.log** - 监控任务状态变化
- **cmd.log** - 监控游戏命令执行
- **进程内存** - 实时游戏状态（英雄选择、战斗状态等）

### 3. 技术实现方向
- **网络包拦截** - 使用WinPcap或类似工具监听游戏网络通信
- **进程注入** - 注入DLL读取游戏内存数据
- **文件监控** - 使用FileSystemWatcher监控日志文件变化
- **窗口检测** - 使用win32gui检测游戏窗口状态

## 结论
7fgame文件夹包含了完整的游戏客户端系统，为自动化提供了丰富的数据源。关键是要区分静态配置数据（JSON文件）和动态运行时数据（日志、内存、网络通信）。自动化系统应该重点关注实时数据获取，而不是依赖本地静态文件。

服务器通信信息显示游戏使用标准的TCP/IP连接和HTTP API，这为网络监听和数据拦截提供了可能性。同时，详细的日志系统也为状态监控提供了备选方案。
