#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块

开发者: @ConceptualGod
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sys
import os
from pathlib import Path


# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import setup_logger
from .account_manager_gui import AccountManagerGUI
from .login_controller import LoginController

class MainWindow:
    """主窗口"""
    
    def __init__(self):
        """初始化主窗口"""
        # 设置日志
        self.logger = setup_logger()
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("起凡自动化脚本 By @ConceptualGod")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标
        self._set_window_icon()
        
        # 创建界面
        self._create_widgets()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        self.logger.info("主窗口初始化完成 - By @ConceptualGod")

    def _set_window_icon(self):
        """
        设置窗口图标
        开发者: @ConceptualGod
        """
        try:
            # 获取logo文件路径
            logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logo", "logo.png")

            if os.path.exists(logo_path):
                # 使用tkinter原生方法设置PNG图标（不依赖PIL）
                try:
                    # 设置窗口图标和任务栏图标
                    self.root.iconphoto(True, tk.PhotoImage(file=logo_path))
                    self.logger.info(f"成功设置窗口图标: {logo_path} - By @ConceptualGod")
                except tk.TclError as e:
                    # 如果PNG不支持，尝试转换为ICO或使用默认图标
                    self.logger.warning(f"PNG图标设置失败，使用默认图标: {str(e)} - By @ConceptualGod")
            else:
                self.logger.warning(f"图标文件不存在: {logo_path} - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"设置窗口图标失败: {str(e)} - By @ConceptualGod")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建菜单栏
        self._create_menu()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 账号管理选项卡
        account_frame = ttk.Frame(self.notebook)
        self.notebook.add(account_frame, text="账号管理")
        
        # 登录控制选项卡
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="登录控制")
        
        # 创建账号管理GUI
        self.account_manager = AccountManagerGUI(account_frame)
        
        # 创建登录控制器
        self.login_controller = LoginController(login_frame)
        
        # 设置回调函数
        self.login_controller.set_account_callbacks(
            self.account_manager.get_accounts
        )
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入账号", command=self._import_accounts)
        file_menu.add_command(label="导出账号", command=self._export_accounts)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清空日志", command=self._clear_logs)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 状态标签
        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 分隔符
        ttk.Separator(self.status_bar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 署名信息
        signature_label = ttk.Label(self.status_bar, text="By @ConceptualGod | v1.2")
        signature_label.pack(side=tk.RIGHT, padx=5)
    
    def _import_accounts(self):
        """导入账号"""
        self.account_manager._import_file()
        self.login_controller._refresh_accounts()
    
    def _export_accounts(self):
        """导出账号"""
        self.account_manager._export_file()
    

    
    def _clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认 - By @ConceptualGod", "确定要清空日志吗？"):
            # 清空登录控制器的状态文本
            self.login_controller.status_text.config(state=tk.NORMAL)
            self.login_controller.status_text.delete('1.0', tk.END)
            self.login_controller.status_text.config(state=tk.DISABLED)
            
            self.status_label.config(text="日志已清空")
    
    def _show_help(self):
        """显示帮助"""
        help_text = """
起凡自动化脚本 By @ConceptualGod

使用说明:
1. 在"账号管理"选项卡中导入或添加账号
2. 在"登录控制"选项卡中选择登录模式
3. 点击"开始登录"执行自动化

支持功能:
- 导入CSV/Excel格式的账号文件(支持中文列名)
- 自动去重，跳过重复账号
- 单号登录和多号轮换
- 登录状态实时监控
- 自动重试机制
- 所有日志在GUI中显示

注意事项:
- 确保游戏已启动
- 确保模板文件完整
- 建议先测试单号登录
- 支持"账号"和"密码"中文列名
        """

        messagebox.showinfo("使用说明 - By @ConceptualGod", help_text)
    
    def _show_about(self):
        """显示关于"""
        about_text = """
起凡自动化脚本 v1.2
By @ConceptualGod
        """
        
        messagebox.showinfo("关于 - By @ConceptualGod", about_text)
    
    def _on_closing(self):
        """窗口关闭事件"""
        try:
            # 停止登录操作
            if hasattr(self.login_controller, 'is_running') and self.login_controller.is_running:
                if messagebox.askyesno("确认 - By @ConceptualGod", "登录正在进行中，确定要退出吗？"):
                    self.login_controller._stop_login()
                else:
                    return
            
            self.logger.info("程序退出")
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"程序退出异常: {str(e)}")
            self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        try:
            self.logger.info("启动GUI界面 - By @ConceptualGod")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"GUI运行异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"程序运行异常: {str(e)}")


def main():
    """主函数"""
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)} - By @ConceptualGod")
        messagebox.showerror("错误 - By @ConceptualGod", f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
