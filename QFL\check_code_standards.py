#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码规范检查脚本
自动检查项目代码是否符合开发规范
开发者: @ConceptualGod
"""

import os
import re
from pathlib import Path


class CodeStandardsChecker:
    """代码规范检查器"""
    
    def __init__(self, project_root: str):
        """
        初始化检查器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root)
        self.emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002500-\U00002BEF\U00002702-\U000027B0\U00002702-\U000027B0\U000024C2-\U0001F251\U0001f926-\U0001f937\U00010000-\U0010ffff\u2640-\u2642\u2600-\u2B55\u200d\u23cf\u23e9\u231a\ufe0f\u3030]')
        self.violations = []
    
    def check_emoji_usage(self):
        """检查emoji符号使用情况"""
        print("[信息] 开始检查emoji符号使用情况 - By @ConceptualGod")

        # 扩展的emoji符号列表 - 根据代码开发规范文档
        forbidden_chars = [
            # 常见emoji
            '✅', '❌', '⚠️', '🔄', '📋', '🎯', '🔧', '📁', '🚀', '📊', '🔍', '💡', '🎮', '🌟', '🔥', '💻', '📝', '🎨', '🛠️', '⭐', '🎉',
            # 其他特殊符号
            '●', '✓', '✗', '⏰', '🗑️', '❓', '📸', '⏸️', '📍', '🎪', '🎭', '🎨', '🎯', '🎲', '🎳', '🎴', '🎵', '🎶', '🎷', '🎸', '🎹', '🎺',
            # Unicode装饰符号
            '▶', '⏸', '⏹', '⏭', '⏮', '⏯', '⏺', '⏻', '⏼', '⏽', '⏾', '⏿', '▀', '▁', '▂', '▃', '▄', '▅', '▆', '▇', '█',
            # 箭头符号
            '→', '←', '↑', '↓', '↔', '↕', '↖', '↗', '↘', '↙', '↺', '↻', '⇒', '⇐', '⇑', '⇓', '⇔', '⇕'
        ]

        # 检查所有Python文件中的emoji使用
        for file_path in self.project_root.rglob("*.py"):
            if "__pycache__" in str(file_path) or "venv" in str(file_path) or file_path.name == "check_code_standards.py":
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                lines = content.split('\n')
                emoji_lines = []

                for line_num, line in enumerate(lines, 1):
                    # 检查是否包含禁用的符号
                    found_chars = []
                    for char in forbidden_chars:
                        if char in line:
                            found_chars.append(char)

                    if found_chars:
                        emoji_lines.append((line_num, line.strip(), found_chars))

                if emoji_lines:
                    self.violations.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "type": "emoji_in_code",
                        "count": len(emoji_lines),
                        "lines": emoji_lines[:10]  # 显示前10行
                    })
                    print(f"[警告] 在 {file_path.relative_to(self.project_root)} 中发现 {len(emoji_lines)} 行包含禁用符号 - By @ConceptualGod")

            except Exception as e:
                print(f"[错误] 读取文件 {file_path} 时出错: {str(e)} - By @ConceptualGod")
    
    def check_signatures(self):
        """检查开发者署名"""
        print("[信息] 开始检查开发者署名 - By @ConceptualGod")
        
        # 检查Python文件的文件头署名
        for file_path in self.project_root.rglob("*.py"):
            if "__pycache__" in str(file_path) or "venv" in str(file_path) or file_path.name == "check_code_standards.py":
                continue
                
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # 检查文件头是否有署名
                if "@ConceptualGod" not in content[:200]:  # 检查前200个字符
                    self.violations.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "type": "missing_signature",
                        "description": "文件头缺少开发者署名"
                    })
                    print(f"[警告] {file_path.relative_to(self.project_root)} 文件头缺少开发者署名 - By @ConceptualGod")
            except Exception as e:
                print(f"[错误] 读取文件 {file_path} 时出错: {str(e)} - By @ConceptualGod")
    
    def check_gui_signatures(self):
        """检查GUI界面署名"""
        print("[信息] 开始检查GUI界面署名 - By @ConceptualGod")
        
        gui_files = list(self.project_root.glob("gui/*.py"))
        for file_path in gui_files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    
                # 检查GUI标题署名
                if "By @ConceptualGod" not in content:
                    self.violations.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "type": "missing_gui_signature",
                        "description": "GUI界面缺少开发者署名"
                    })
                    print(f"[警告] {file_path.relative_to(self.project_root)} GUI界面缺少开发者署名 - By @ConceptualGod")
            except Exception as e:
                print(f"[错误] 读取文件 {file_path} 时出错: {str(e)} - By @ConceptualGod")
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*60)
        print("代码规范检查报告")
        print("开发者: @ConceptualGod")
        print("="*60)
        
        if not self.violations:
            print("[成功] 所有代码均符合开发规范！ - By @ConceptualGod")
        else:
            print(f"[警告] 发现 {len(self.violations)} 个规范违反项 - By @ConceptualGod")
            print("-"*60)
            
            for i, violation in enumerate(self.violations, 1):
                print(f"{i}. 文件: {violation['file']}")
                print(f"   类型: {violation['type']}")
                if "description" in violation:
                    print(f"   描述: {violation['description']}")
                if "count" in violation:
                    print(f"   数量: {violation['count']}")
                if "emojis" in violation:
                    print(f"   Emoji: {' '.join(violation['emojis'])}")
                if "lines" in violation:
                    print(f"   问题行:")
                    for item in violation['lines']:
                        if len(item) == 3:  # emoji检查结果
                            line_num, line_content, found_chars = item
                            print(f"     第{line_num}行: {line_content}")
                            print(f"       发现符号: {', '.join(found_chars)}")
                        else:  # 其他检查结果
                            line_num, line_content = item
                            print(f"     第{line_num}行: {line_content}")
                if "line" in violation:
                    print(f"   行号: {violation['line']}")
                if "content" in violation:
                    print(f"   内容: {violation['content']}")
                print()
        
        print("="*60)
    
    def check_log_message_standards(self):
        """检查日志消息规范"""
        print("[信息] 开始检查日志消息规范 - By @ConceptualGod")

        # 日志方法模式
        log_patterns = [
            'self._log(',
            'logger.info(',
            'logger.error(',
            'logger.warning(',
            'logger.debug(',
            'print(',
            'messagebox.showinfo(',
            'messagebox.showerror(',
            'messagebox.showwarning(',
            'messagebox.askyesno('
        ]

        for file_path in self.project_root.rglob("*.py"):
            if "__pycache__" in str(file_path) or "venv" in str(file_path) or file_path.name == "check_code_standards.py":
                continue

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                lines = content.split('\n')

                for line_num, line in enumerate(lines, 1):
                    # 检查日志消息
                    for pattern in log_patterns:
                        if pattern in line:
                            # 检查是否缺少署名（对于用户可见的消息）
                            if any(keyword in line.lower() for keyword in ['成功', '完成', '启动', '错误', '警告', '失败', '开始']):
                                if "By @ConceptualGod" not in line and "# " not in line:  # 排除注释行
                                    self.violations.append({
                                        "file": str(file_path.relative_to(self.project_root)),
                                        "type": "log_message_missing_signature",
                                        "line": line_num,
                                        "content": line.strip(),
                                        "description": "用户可见消息缺少开发者署名"
                                    })
                                    print(f"[警告] {file_path.relative_to(self.project_root)}:{line_num} 消息缺少署名 - By @ConceptualGod")
                            break

            except Exception as e:
                print(f"[错误] 读取文件 {file_path} 时出错: {str(e)} - By @ConceptualGod")

    def run(self):
        """运行所有检查"""
        print("[信息] 启动代码规范检查 - By @ConceptualGod")
        print("-"*50)

        self.check_emoji_usage()
        self.check_signatures()
        self.check_gui_signatures()
        self.check_log_message_standards()

        self.generate_report()


def main():
    """主函数"""
    project_root = Path(__file__).parent
    checker = CodeStandardsChecker(project_root)
    checker.run()


if __name__ == "__main__":
    main()
