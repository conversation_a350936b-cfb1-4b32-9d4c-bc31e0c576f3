#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录控制器模块

开发者: @ConceptualGod
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import logging
import json
import os
from typing import List, Dict, Any, Optional, Callable, Tuple
import pyautogui
import sys
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.config_manager import ConfigManager

class GameAutomation:
    """起凡游戏窗口自动化类"""

    def __init__(self):
        self.is_logged_in = False
        self.game_window_title = ""
        self.game_window_handle = None

    def detect_game_window(self) -> bool:
        """检测起凡游戏窗口"""
        try:
            import win32gui
            import win32con

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if "起凡游戏平台" in window_title:
                        windows.append((hwnd, window_title))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                # 找到游戏窗口
                self.game_window_handle, self.game_window_title = windows[0]

                # 判断登录状态：未登录窗口包含发布时间，已登录窗口不包含
                if "发布时间" in self.game_window_title:
                    self.is_logged_in = False
                else:
                    self.is_logged_in = True

                return True
            else:
                self.game_window_handle = None
                self.game_window_title = ""
                self.is_logged_in = False
                return False

        except ImportError:
            # 如果没有win32gui，使用备用方法
            return self._detect_window_fallback()
        except Exception as e:
            print(f"检测游戏窗口异常: {e}")
            return False

    def _detect_window_fallback(self) -> bool:
        """备用窗口检测方法"""
        try:
            import psutil

            # 查找起凡游戏进程
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and '起凡' in proc.info['name']:
                        self.game_window_title = "起凡游戏平台"
                        self.is_logged_in = False  # 无法精确判断，默认未登录
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        except ImportError:
            # 如果psutil也没有，返回False
            return False

    def bring_window_to_front(self) -> bool:
        """将游戏窗口置于前台"""
        try:
            import win32gui
            import win32con

            if self.game_window_handle:
                # 恢复窗口（如果最小化）
                win32gui.ShowWindow(self.game_window_handle, win32con.SW_RESTORE)
                # 置于前台
                win32gui.SetForegroundWindow(self.game_window_handle)
                return True
            return False

        except ImportError:
            # 如果没有win32gui，使用pyautogui的备用方法
            return self._bring_window_to_front_fallback()
        except Exception as e:
            print(f"窗口置前异常: {e}")
            return False

    def _bring_window_to_front_fallback(self) -> bool:
        """备用窗口置前方法"""
        try:
            # 使用Alt+Tab切换窗口的方法
            pyautogui.hotkey('alt', 'tab')
            time.sleep(0.5)
            return True
        except Exception:
            return False

    def input_text_via_clipboard(self, text: str) -> bool:
        """通过剪贴板输入文本"""
        try:
            import pyperclip
            pyperclip.copy(text)
            pyautogui.hotkey('ctrl', 'v')
            return True
        except ImportError:
            # 如果没有pyperclip，返回False让调用者使用备用方法
            return False
        except Exception:
            return False

class LoginController:
    """登录控制器"""
    
    def __init__(self, parent_frame):
        """
        初始化登录控制器
        
        Args:
            parent_frame: 父级框架
        """
        self.parent_frame = parent_frame
        self.logger = logging.getLogger(__name__)
        
        # 初始化核心组件
        self.config_manager = ConfigManager()

        # 创建游戏自动化对象
        self.automation = GameAutomation()
        
        # 登录状态
        self.is_running = False
        self.current_account = None
        self.login_thread = None
        self.last_logged_username = ""  # 记录上一个登录的账号名

        # 登录坐标配置
        self.login_coordinates = []
        self.login_config_file = "login.json"

        # 回调函数
        self.get_accounts_callback = None

        # 加载登录坐标配置
        self._load_login_coordinates()
        
        # 创建界面
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 登录模式选择框架
        mode_frame = ttk.LabelFrame(main_frame, text="登录模式")
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 登录模式变量
        self.login_mode = tk.StringVar(value="single")
        
        ttk.Radiobutton(mode_frame, text="单号登录", variable=self.login_mode, 
                       value="single").pack(side=tk.LEFT, padx=10, pady=5)
        ttk.Radiobutton(mode_frame, text="多号轮换", variable=self.login_mode, 
                       value="multiple").pack(side=tk.LEFT, padx=10, pady=5)
        
        # 账号选择框架
        account_frame = ttk.LabelFrame(main_frame, text="账号选择")
        account_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 账号选择下拉框
        ttk.Label(account_frame, text="选择账号:").pack(side=tk.LEFT, padx=(10, 5), pady=5)
        self.account_combo = ttk.Combobox(account_frame, state='readonly', width=30)
        self.account_combo.pack(side=tk.LEFT, padx=(0, 10), pady=5)
        
        # 刷新账号按钮
        ttk.Button(account_frame, text="刷新账号", command=self._refresh_accounts).pack(side=tk.LEFT, pady=5)
        
        # 控制按钮框架
        control_frame = ttk.LabelFrame(main_frame, text="登录控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 控制按钮
        self.start_button = ttk.Button(control_frame, text="开始轮登", command=self._start_login)
        self.start_button.pack(side=tk.LEFT, padx=10, pady=5)

        self.stop_button = ttk.Button(control_frame, text="停止轮登", command=self._stop_login, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 登录设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="登录设置")
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 登录间隔设置
        ttk.Label(settings_frame, text="账号间隔(秒):").pack(side=tk.LEFT, padx=(10, 5), pady=5)
        self.interval_var = tk.StringVar(value="5")
        interval_entry = ttk.Entry(settings_frame, textvariable=self.interval_var, width=10)
        interval_entry.pack(side=tk.LEFT, padx=(0, 10), pady=5)
        
        # 重试次数设置
        ttk.Label(settings_frame, text="重试次数:").pack(side=tk.LEFT, padx=(10, 5), pady=5)
        self.retry_var = tk.StringVar(value="3")
        retry_entry = ttk.Entry(settings_frame, textvariable=self.retry_var, width=10)
        retry_entry.pack(side=tk.LEFT, padx=(0, 10), pady=5)
        
        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="登录状态")
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        # 状态文本框
        self.status_text = tk.Text(status_frame, height=10, state=tk.DISABLED)
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 状态和署名框架
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=5)

        # 当前状态标签
        self.current_status_label = ttk.Label(bottom_frame, text="状态: 就绪")
        self.current_status_label.pack(side=tk.LEFT)

        # 署名水印
        signature_label = ttk.Label(bottom_frame, text="By @ConceptualGod",
                                   foreground="gray", font=("Arial", 8))
        signature_label.pack(side=tk.RIGHT)
    
    def set_account_callbacks(self, get_accounts_func: Callable):
        """
        设置获取账号的回调函数

        Args:
            get_accounts_func: 获取所有账号的函数
        """
        self.get_accounts_callback = get_accounts_func
        self._refresh_accounts()
    
    def _refresh_accounts(self):
        """刷新账号列表"""
        try:
            if self.get_accounts_callback:
                accounts = self.get_accounts_callback()
                account_names = [acc['username'] for acc in accounts]
                self.account_combo['values'] = account_names

                if account_names:
                    self.account_combo.current(0)

                self._log_status(f"刷新账号列表，共 {len(accounts)} 个账号")

        except Exception as e:
            self.logger.error(f"刷新账号列表异常: {str(e)}")
            self._log_status(f"刷新账号列表失败: {str(e)}")
    
    def _start_login(self):
        """开始登录"""
        try:
            if self.is_running:
                messagebox.showwarning("警告 - By @ConceptualGod", "登录已在进行中")
                return
            
            # 获取登录参数
            mode = self.login_mode.get()
            
            if mode == "single":
                # 单号登录
                if not self.account_combo.get():
                    messagebox.showwarning("警告 - By @ConceptualGod", "请选择要登录的账号")
                    return
                
                selected_index = self.account_combo.current()
                if self.get_accounts_callback:
                    accounts = self.get_accounts_callback()
                    if 0 <= selected_index < len(accounts):
                        selected_account = accounts[selected_index]
                        self._start_single_login(selected_account)
            else:
                # 多号轮换
                if self.get_accounts_callback:
                    all_accounts = self.get_accounts_callback()
                    if not all_accounts:
                        messagebox.showwarning("警告 - By @ConceptualGod", "没有账号可以登录")
                        return
                    self._start_multiple_login(all_accounts)
            
        except Exception as e:
            self.logger.error(f"开始登录异常: {str(e)} - By @ConceptualGod")
            messagebox.showerror("错误 - By @ConceptualGod", f"开始登录失败: {str(e)}")
    
    def _start_single_login(self, account: Dict[str, Any]):
        """
        开始单号登录
        
        Args:
            account: 账号信息
        """
        self.is_running = True
        self._update_button_state()
        self._log_status(f"开始单号登录: {account['username']}")
        
        # 在新线程中执行登录
        self.login_thread = threading.Thread(target=self._single_login_worker, args=(account,))
        self.login_thread.daemon = True
        self.login_thread.start()
    
    def _start_multiple_login(self, accounts: List[Dict[str, Any]]):
        """
        开始多号轮换登录
        
        Args:
            accounts: 账号列表
        """
        self.is_running = True
        self._update_button_state()
        self._log_status(f"开始多号轮换登录，共 {len(accounts)} 个账号")
        
        # 在新线程中执行登录
        self.login_thread = threading.Thread(target=self._multiple_login_worker, args=(accounts,))
        self.login_thread.daemon = True
        self.login_thread.start()
    
    def _single_login_worker(self, account: Dict[str, Any]):
        """
        单号登录工作线程

        Args:
            account: 账号信息

        开发者: @ConceptualGod
        """
        try:
            self.current_account = account
            self._update_current_status(f"检测游戏窗口...")

            # 第一步：检测游戏窗口
            if not self.automation.detect_game_window():
                self._update_current_status("未检测到游戏窗口")
                self._log_status("错误: 未检测到游戏窗口，请先启动游戏 - By @ConceptualGod")
                return

            # 第二步：将游戏窗口置于前台
            self._update_current_status("准备游戏窗口...")
            if not self.automation.bring_window_to_front():
                self._update_current_status("窗口置前失败")
                self._log_status("错误: 无法将游戏窗口置于前台 - By @ConceptualGod")
                return

            # 等待窗口稳定
            time.sleep(1)

            # 第三步：开始登录流程
            self._update_current_status(f"正在登录: {account['username']}")

            # 执行登录
            success = self._perform_login(account)
            
            if success:
                self._log_status(f"账号 {account['username']} 登录成功")
            else:
                self._log_status(f"账号 {account['username']} 登录失败")
            
        except Exception as e:
            self.logger.error(f"单号登录异常: {str(e)}")
            self._log_status(f"单号登录异常: {str(e)}")
        finally:
            self.is_running = False
            self.current_account = None
            self._update_button_state()
            self._update_current_status("登录完成")
    
    def _multiple_login_worker(self, accounts: List[Dict[str, Any]]):
        """
        多号轮换登录工作线程

        Args:
            accounts: 账号列表

        开发者: @ConceptualGod
        """
        try:
            interval = int(self.interval_var.get())

            for i, account in enumerate(accounts):
                if not self.is_running:
                    break

                self.current_account = account
                self._update_current_status(f"检测游戏窗口...")

                # 第一步：检测游戏窗口
                if not self.automation.detect_game_window():
                    self._update_current_status("未检测到游戏窗口")
                    self._log_status(f"第 {i+1} 个账号: 未检测到游戏窗口，跳过 - By @ConceptualGod")
                    continue

                # 第二步：将游戏窗口置于前台
                self._update_current_status("准备游戏窗口...")
                if not self.automation.bring_window_to_front():
                    self._update_current_status("窗口置前失败")
                    self._log_status(f"第 {i+1} 个账号: 无法将游戏窗口置于前台，跳过 - By @ConceptualGod")
                    continue

                # 等待窗口稳定
                time.sleep(1)

                # 第三步：执行登录
                self._update_current_status(f"正在登录: {account['username']} ({i+1}/{len(accounts)})")
                self._log_status(f"开始登录账号 {i+1}/{len(accounts)}: {account['username']} - By @ConceptualGod")

                # 执行登录
                success = self._perform_multiple_login(account, i+1, len(accounts))

                if success:
                    self._log_status(f"账号 {account['username']} 完整流程执行成功 - By @ConceptualGod")

                    # 如果不是最后一个账号，需要准备下一个账号
                    if i < len(accounts) - 1 and self.is_running:
                        next_account = accounts[i+1]
                        self._prepare_next_account(next_account['username'])
                else:
                    self._log_status(f"账号 {account['username']} 流程执行失败 - By @ConceptualGod")

                # 账号间隔
                if i < len(accounts) - 1 and self.is_running:
                    self._log_status(f"等待 {interval} 秒后继续下一个账号... - By @ConceptualGod")
                    time.sleep(interval)
            
        except Exception as e:
            self.logger.error(f"多号轮换登录异常: {str(e)}")
            self._log_status(f"多号轮换登录异常: {str(e)}")
        finally:
            self.is_running = False
            self.current_account = None
            self._update_button_state()
            self._update_current_status("所有账号登录完成")
    
    def _perform_login(self, account: Dict[str, Any]) -> bool:
        """
        执行登录操作

        Args:
            account: 账号信息

        Returns:
            是否登录成功

        开发者: @ConceptualGod
        """
        try:
            username = account.get('username', '')
            password = account.get('password', '')

            if not username or not password:
                self._log_status("错误: 账号或密码为空 - By @ConceptualGod")
                return False

            if not self.login_coordinates:
                self._log_status("错误: 未加载登录坐标配置 - By @ConceptualGod")
                return False

            self._log_status(f"开始登录账号: {username} - 使用JSON坐标配置 - By @ConceptualGod")

            # 按步骤执行登录坐标
            for coord in self.login_coordinates:
                step = coord.get("step", 0)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"执行第 {step} 步: {description}...")
                self._log_status(f"第 {step} 步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 点击坐标
                pyautogui.click(x, y)
                time.sleep(0.5)  # 等待界面响应

                # 根据步骤执行相应操作
                if step == 1:  # 账号输入框
                    self._update_current_status("输入账号...")
                    if not self._input_text(username):
                        self._log_status("错误: 输入账号失败 - By @ConceptualGod")
                        return False
                elif step == 2:  # 密码输入框
                    self._update_current_status("输入密码...")
                    if not self._input_text(password):
                        self._log_status("错误: 输入密码失败 - By @ConceptualGod")
                        return False
                elif step == 3:  # 登录按钮
                    self._update_current_status("点击登录按钮...")
                    # 登录按钮已经点击，无需额外操作

                # 更新坐标状态
                coord["status"] = "已执行"

                # 步骤间隔
                time.sleep(0.5)

            # 等待登录结果
            login_success = self._wait_for_login_result()

            if login_success:
                self._log_status(f"账号 {username} 登录成功 - By @ConceptualGod")

                # 记录当前登录的账号名
                self.last_logged_username = username
                self._log_status(f"记录当前登录账号名: {username} - By @ConceptualGod")

                # 单号登录不执行游戏操作，直接返回
                return True
            else:
                self._log_status(f"账号 {username} 登录失败或超时 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"登录执行异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"登录异常: {str(e)} - By @ConceptualGod")
            return False

    def _perform_multiple_login(self, account: Dict[str, Any], account_index: int, total_accounts: int) -> bool:
        """
        执行多号登录操作（包含游戏操作和退出）

        Args:
            account: 账号信息
            account_index: 当前账号索引
            total_accounts: 总账号数

        Returns:
            是否执行成功

        开发者: @ConceptualGod
        """
        try:
            username = account.get('username', '')
            password = account.get('password', '')

            if not username or not password:
                self._log_status(f"第{account_index}个账号信息不完整 - By @ConceptualGod")
                return False

            if not self.login_coordinates:
                self._log_status(f"第{account_index}个账号: 未加载登录坐标配置 - By @ConceptualGod")
                return False

            self._log_status(f"第{account_index}/{total_accounts}个账号开始登录: {username} - By @ConceptualGod")

            # 按步骤执行登录坐标
            for coord in self.login_coordinates:
                step = coord.get("step", 0)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"第{account_index}个账号 - 第{step}步: {description}...")
                self._log_status(f"第{account_index}个账号 - 第{step}步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 点击坐标
                pyautogui.click(x, y)
                time.sleep(0.5)

                # 根据步骤执行相应操作
                if step == 1:  # 账号输入框
                    self._update_current_status(f"第{account_index}个账号 - 输入账号...")
                    if not self._input_text(username):
                        self._log_status(f"第{account_index}个账号: 输入账号失败 - By @ConceptualGod")
                        return False
                elif step == 2:  # 密码输入框
                    self._update_current_status(f"第{account_index}个账号 - 输入密码...")
                    if not self._input_text(password):
                        self._log_status(f"第{account_index}个账号: 输入密码失败 - By @ConceptualGod")
                        return False
                elif step == 3:  # 登录按钮
                    self._update_current_status(f"第{account_index}个账号 - 点击登录按钮...")

                coord["status"] = "已执行"
                time.sleep(0.5)

            # 等待登录结果（多号登录专用，包含15秒网络延迟等待）
            login_success = self._wait_for_login_result_multiple(account_index)

            if login_success:
                self._log_status(f"第{account_index}个账号 {username} 登录成功 - By @ConceptualGod")

                # 记录当前登录的账号名
                self.last_logged_username = username
                self._log_status(f"记录当前登录账号名: {username} - By @ConceptualGod")

                # 执行游戏操作
                self._execute_game_operations_for_multiple(username, account_index)

                return True
            else:
                self._log_status(f"第{account_index}个账号 {username} 登录失败或超时 - By @ConceptualGod")
                return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号登录执行异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号登录异常: {str(e)} - By @ConceptualGod")
            return False

    # 注意：原有的句柄检测方法已移除，现在完全使用JSON坐标配置
    # _find_login_dialog, _find_username_input, _find_login_button 方法已不再需要

    def _click_element(self, position: Tuple[int, int]) -> bool:
        """
        点击指定位置

        Args:
            position: 点击位置 (x, y)

        Returns:
            是否点击成功

        开发者: @ConceptualGod
        """
        try:
            x, y = position

            # 双击以确保选中输入框
            pyautogui.doubleClick(x, y)
            time.sleep(0.2)

            self.logger.debug(f"点击位置: ({x}, {y}) - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"点击操作异常 - By @ConceptualGod: {str(e)}")
            return False

    def _input_text(self, text: str) -> bool:
        """
        输入文本

        Args:
            text: 要输入的文本

        Returns:
            是否输入成功

        开发者: @ConceptualGod
        """
        try:
            # 根据上一个账号名长度确定退格键次数
            if self.last_logged_username:
                # 计算上一个账号名长度
                last_username_length = len(self.last_logged_username)
                # 多删除10个字符确保完全清空
                total_backspace = last_username_length + 10
                self.logger.debug(f"根据上一个账号 {self.last_logged_username} 长度({last_username_length})，退格{total_backspace}次 - By @ConceptualGod")
            else:
                # 如果没有上一个账号记录，默认删除20次
                total_backspace = 20
                self.logger.debug(f"没有上一个账号记录，默认退格{total_backspace}次 - By @ConceptualGod")

            # 使用退格键清空输入框
            for i in range(total_backspace):
                pyautogui.press('backspace')
                time.sleep(0.05)  # 快速按键间隔

            # 通过剪贴板输入文本（支持中文）
            if not self.automation.input_text_via_clipboard(text):
                # 如果剪贴板输入失败，尝试直接输入
                pyautogui.write(text)

            time.sleep(0.2)
            self.logger.debug(f"输入文本完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"输入文本异常 - By @ConceptualGod: {str(e)}")
            return False

    def _wait_for_login_result(self) -> bool:
        """
        等待登录结果，通过检测窗口标题变化判断登录状态

        Returns:
            是否登录成功

        开发者: @ConceptualGod
        """
        try:
            self._update_current_status("等待登录结果...")
            self.logger.info("开始等待登录结果，检测窗口标题变化 - By @ConceptualGod")

            # 等待15秒，期间检测窗口标题变化
            max_wait_time = 15  # 等待15秒
            check_interval = 1  # 每1秒检测一次

            for i in range(max_wait_time):
                if not self.is_running:
                    return False

                # 重新检测游戏窗口状态
                if self.automation.detect_game_window():
                    # 检查是否已经登录（窗口标题变化）
                    if self.automation.is_logged_in:
                        self._update_current_status("登录成功")
                        self.logger.info(f"检测到登录成功，窗口标题: {self.automation.game_window_title} - By @ConceptualGod")
                        return True
                    else:
                        # 仍在登录界面，继续等待
                        self._update_current_status(f"等待登录结果... ({i+1}/{max_wait_time}秒)")
                        self.logger.debug(f"仍在登录界面，继续等待... ({i+1}/{max_wait_time}秒) - By @ConceptualGod")
                else:
                    # 窗口消失或检测失败
                    self.logger.warning(f"窗口检测失败 ({i+1}/{max_wait_time}秒) - By @ConceptualGod")

                time.sleep(check_interval)

            # 超时未检测到登录成功
            self._update_current_status("登录超时")
            self.logger.warning("登录等待超时，未检测到窗口标题变化 - By @ConceptualGod")
            return False

        except Exception as e:
            self.logger.error(f"等待登录结果异常 - By @ConceptualGod: {str(e)}")
            self._update_current_status("登录检测异常")
            return False

    def _wait_for_login_result_multiple(self, account_index: int) -> bool:
        """
        多号登录专用的等待登录结果方法（包含网络延迟等待）

        Args:
            account_index: 账号索引

        Returns:
            是否登录成功

        开发者: @ConceptualGod
        """
        try:
            self._update_current_status(f"第{account_index}个账号 - 等待登录结果...")
            self.logger.info(f"第{account_index}个账号开始等待登录结果，包含15秒网络延迟等待 - By @ConceptualGod")

            # 等待15秒防止网络延迟
            network_wait_time = 15
            check_interval = 1

            for i in range(network_wait_time):
                if not self.is_running:
                    return False

                # 重新检测游戏窗口状态
                if self.automation.detect_game_window():
                    # 检查是否已经登录（窗口标题变化）
                    if self.automation.is_logged_in:
                        self._update_current_status(f"第{account_index}个账号 - 登录成功")
                        self.logger.info(f"第{account_index}个账号检测到登录成功，窗口标题: {self.automation.game_window_title} - By @ConceptualGod")

                        # 登录成功后等待20秒再执行游戏操作
                        game_wait_time = 20
                        self._log_status(f"第{account_index}个账号登录成功，等待{game_wait_time}秒后开始游戏操作 - By @ConceptualGod")
                        self._update_current_status(f"第{account_index}个账号 - 等待游戏界面稳定...")

                        for j in range(game_wait_time):
                            if not self.is_running:
                                return False
                            self._update_current_status(f"第{account_index}个账号 - 等待游戏界面稳定... ({j+1}/{game_wait_time}秒)")
                            time.sleep(1)

                        return True
                    else:
                        # 仍在登录界面，继续等待
                        self._update_current_status(f"第{account_index}个账号 - 等待登录结果... ({i+1}/{network_wait_time}秒)")
                        self.logger.debug(f"第{account_index}个账号仍在登录界面，继续等待... ({i+1}/{network_wait_time}秒) - By @ConceptualGod")
                else:
                    # 窗口消失或检测失败
                    self.logger.warning(f"第{account_index}个账号窗口检测失败 ({i+1}/{network_wait_time}秒) - By @ConceptualGod")

                time.sleep(check_interval)

            # 超时未检测到登录成功
            self._update_current_status(f"第{account_index}个账号 - 登录超时")
            self.logger.warning(f"第{account_index}个账号登录等待超时，未检测到窗口标题变化 - By @ConceptualGod")
            return False

        except Exception as e:
            self.logger.error(f"第{account_index}个账号等待登录结果异常 - By @ConceptualGod: {str(e)}")
            self._update_current_status(f"第{account_index}个账号 - 登录检测异常")
            return False


    def _execute_game_operations_for_multiple(self, username: str, account_index: int):
        """
        为多号登录执行游戏内操作

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始执行游戏内操作 - By @ConceptualGod")
            self._update_current_status(f"第{account_index}个账号 - 执行游戏内操作...")

            # 执行游戏任务前再等待10秒确保界面完全稳定
            pre_game_wait_time = 10
            self._log_status(f"第{account_index}个账号执行游戏任务前，再等待{pre_game_wait_time}秒确保界面完全稳定 - By @ConceptualGod")

            for i in range(pre_game_wait_time):
                if not self.is_running:
                    return
                self._update_current_status(f"第{account_index}个账号 - 游戏任务前等待... ({i+1}/{pre_game_wait_time}秒)")
                time.sleep(1)

            # 执行 coordinates_1.json 操作
            if self._execute_coordinate_file_for_multiple("coordinates_1.json", "游戏内任务操作", account_index):
                self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作完成 - By @ConceptualGod")

                # 等待操作完成
                time.sleep(1)

                # 执行 close.json 操作
                if self._execute_coordinate_file_for_multiple("close.json", "关闭界面操作", account_index):
                    self._log_status(f"第{account_index}个账号 {username} 关闭界面操作完成 - By @ConceptualGod")

                    # 等待关闭操作完成
                    time.sleep(1)

                    # 执行 coordinates_2.json 操作
                    if self._execute_coordinate_file_for_multiple("coordinates_2.json", "游戏内任务操作2", account_index):
                        self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作2完成 - By @ConceptualGod")

                        # 等待操作完成
                        time.sleep(1)

                        # 任务英雄识别模块不存在，跳过
                        self._log_status(f"第{account_index}个账号 {username} 跳过任务英雄识别 (模块不存在) - By @ConceptualGod")

                        # 执行 exit.json 操作
                        if self._execute_coordinate_file_for_multiple("exit.json", "退出账号操作", account_index):
                            self._log_status(f"第{account_index}个账号 {username} 退出账号操作完成 - By @ConceptualGod")

                            # 等待退出完成，检测回到登录平台
                            self._wait_for_logout_to_login_platform(username, account_index)
                        else:
                            self._log_status(f"第{account_index}个账号 {username} 退出账号操作失败 - By @ConceptualGod")
                    else:
                        self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作2失败 - By @ConceptualGod")
                else:
                    self._log_status(f"第{account_index}个账号 {username} 关闭界面操作失败 - By @ConceptualGod")
            else:
                self._log_status(f"第{account_index}个账号 {username} 游戏内任务操作失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"第{account_index}个账号游戏操作异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 游戏操作异常: {str(e)} - By @ConceptualGod")



    def _execute_game_operations(self, username: str):
        """
        执行游戏内操作

        Args:
            username: 当前账号用户名

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"账号 {username} 开始执行游戏内操作 - By @ConceptualGod")
            self._update_current_status("执行游戏内操作...")

            # 等待游戏界面稳定
            time.sleep(2)

            # 执行 coordinates_1.json 操作
            if self._execute_coordinate_file("coordinates_1.json", "游戏内任务操作"):
                self._log_status(f"账号 {username} 游戏内任务操作完成 - By @ConceptualGod")

                # 等待操作完成
                time.sleep(1)

                # 执行 exit.json 操作
                if self._execute_coordinate_file("exit.json", "退出账号操作"):
                    self._log_status(f"账号 {username} 退出账号操作完成 - By @ConceptualGod")
                else:
                    self._log_status(f"账号 {username} 退出账号操作失败 - By @ConceptualGod")
            else:
                self._log_status(f"账号 {username} 游戏内任务操作失败 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"执行游戏操作异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"账号 {username} 游戏操作异常: {str(e)} - By @ConceptualGod")

    def _execute_coordinate_file(self, filename: str, operation_name: str) -> bool:
        """
        执行指定的坐标文件操作

        Args:
            filename: 坐标文件名
            operation_name: 操作名称

        Returns:
            是否执行成功

        开发者: @ConceptualGod
        """
        try:
            # 加载坐标文件
            coordinates = self._load_coordinate_file(filename)
            if not coordinates:
                self._log_status(f"加载坐标文件失败: {filename} - By @ConceptualGod")
                return False

            self._log_status(f"开始执行 {operation_name} (文件: {filename}) - By @ConceptualGod")
            self._update_current_status(f"执行{operation_name}...")

            # 逐步执行坐标操作
            for i, coord in enumerate(coordinates):
                if not self.is_running:
                    self._log_status(f"{operation_name} 被用户中断 - By @ConceptualGod")
                    return False

                step = coord.get("step", i+1)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"{operation_name} - 第{step}步: {description}")
                self._log_status(f"第{step}步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 执行点击操作
                pyautogui.click(x, y)

                # 记录操作日志
                self.logger.info(f"{operation_name} - 第{step}步执行完成: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 步骤间隔
                time.sleep(1)

            self._log_status(f"{operation_name} 全部步骤执行完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"执行坐标文件异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"{operation_name} 执行异常: {str(e)} - By @ConceptualGod")
            return False

    def _load_coordinate_file(self, filename: str) -> List[Dict[str, Any]]:
        """
        加载坐标文件

        Args:
            filename: 坐标文件名

        Returns:
            坐标列表

        开发者: @ConceptualGod
        """
        try:
            file_path = os.path.join(os.path.dirname(__file__), '..', filename)

            if not os.path.exists(file_path):
                self.logger.error(f"坐标文件不存在: {file_path} - By @ConceptualGod")
                return []

            with open(file_path, 'r', encoding='utf-8') as f:
                coordinates = json.load(f)

            self.logger.info(f"成功加载坐标文件: {filename}, 共{len(coordinates)}个步骤 - By @ConceptualGod")
            return coordinates

        except Exception as e:
            self.logger.error(f"加载坐标文件异常 - By @ConceptualGod: {str(e)}")
            return []

    def _execute_coordinate_file_for_multiple(self, filename: str, operation_name: str, account_index: int) -> bool:
        """
        为多号登录执行指定的坐标文件操作

        Args:
            filename: 坐标文件名
            operation_name: 操作名称
            account_index: 账号索引

        Returns:
            是否执行成功

        开发者: @ConceptualGod
        """
        try:
            # 加载坐标文件
            coordinates = self._load_coordinate_file(filename)
            if not coordinates:
                self._log_status(f"第{account_index}个账号: 加载坐标文件失败: {filename} - By @ConceptualGod")
                return False

            self._log_status(f"第{account_index}个账号开始执行 {operation_name} (文件: {filename}) - By @ConceptualGod")
            self._update_current_status(f"第{account_index}个账号 - {operation_name}...")

            # 逐步执行坐标操作
            for i, coord in enumerate(coordinates):
                if not self.is_running:
                    self._log_status(f"第{account_index}个账号 {operation_name} 被用户中断 - By @ConceptualGod")
                    return False

                step = coord.get("step", i+1)
                x = coord.get("x", 0)
                y = coord.get("y", 0)
                description = coord.get("description", "")

                self._update_current_status(f"第{account_index}个账号 {operation_name} - 第{step}步: {description}")
                self._log_status(f"第{account_index}个账号 第{step}步: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 执行点击操作
                pyautogui.click(x, y)

                # 记录操作日志
                self.logger.info(f"第{account_index}个账号 {operation_name} - 第{step}步执行完成: {description} - 坐标({x}, {y}) - By @ConceptualGod")

                # 步骤间隔 - 战功操作需要更长等待时间
                if filename == "coordinates_2.json":
                    # 战功操作每步等待10秒
                    wait_time = 10
                    self._log_status(f"第{account_index}个账号 战功操作等待{wait_time}秒... - By @ConceptualGod")
                    for j in range(wait_time):
                        if not self.is_running:
                            return False
                        self._update_current_status(f"第{account_index}个账号 战功操作等待... ({j+1}/{wait_time}秒)")
                        time.sleep(1)
                else:
                    # 其他操作保持1秒间隔
                    time.sleep(1)

            self._log_status(f"第{account_index}个账号 {operation_name} 全部步骤执行完成 - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"第{account_index}个账号执行坐标文件异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {operation_name} 执行异常: {str(e)} - By @ConceptualGod")
            return False

    def _wait_for_logout_to_login_platform(self, username: str, account_index: int):
        """
        等待退出到登录平台（包含退出后的等待时间）

        Args:
            username: 当前账号用户名
            account_index: 账号索引

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"第{account_index}个账号 {username} 开始等待退出到登录平台... - By @ConceptualGod")
            self._update_current_status(f"第{account_index}个账号 - 等待退出到登录平台...")

            # 先等待5-6秒让退出操作完成
            logout_wait_time = 6
            self._log_status(f"第{account_index}个账号退出操作完成，等待{logout_wait_time}秒后检测登录平台 - By @ConceptualGod")

            for i in range(logout_wait_time):
                if not self.is_running:
                    return
                self._update_current_status(f"第{account_index}个账号 - 等待退出完成... ({i+1}/{logout_wait_time}秒)")
                time.sleep(1)

            # 然后检测是否回到登录平台
            max_detect_time = 20  # 最多检测20秒
            check_interval = 1

            self._log_status(f"第{account_index}个账号开始检测是否回到登录平台 - By @ConceptualGod")

            for i in range(max_detect_time):
                if not self.is_running:
                    return

                # 检测游戏窗口状态
                if self.automation.detect_game_window():
                    # 检查是否回到登录平台（窗口标题包含发布时间）
                    if not self.automation.is_logged_in:
                        self._log_status(f"第{account_index}个账号 {username} 已检测到退出到登录平台，窗口标题: {self.automation.game_window_title} - By @ConceptualGod")

                        # 检测到退出后，再等待15秒让界面完全稳定
                        platform_stable_wait = 15
                        self._log_status(f"第{account_index}个账号检测到退出成功，再等待{platform_stable_wait}秒让登录平台界面完全稳定 - By @ConceptualGod")

                        for j in range(platform_stable_wait):
                            if not self.is_running:
                                return
                            self._update_current_status(f"第{account_index}个账号 - 登录平台界面稳定等待... ({j+1}/{platform_stable_wait}秒)")
                            time.sleep(1)

                        self._log_status(f"第{account_index}个账号 {username} 登录平台界面已完全稳定，可以进行下一步操作 - By @ConceptualGod")
                        self._update_current_status(f"第{account_index}个账号 - 已退出到登录平台")
                        return
                    else:
                        self._update_current_status(f"第{account_index}个账号 - 检测登录平台... ({i+1}/{max_detect_time}秒)")
                        self.logger.debug(f"第{account_index}个账号仍在游戏界面，继续检测... ({i+1}/{max_detect_time}秒) - By @ConceptualGod")
                else:
                    self.logger.warning(f"第{account_index}个账号窗口检测失败 ({i+1}/{max_detect_time}秒) - By @ConceptualGod")

                time.sleep(check_interval)

            self._log_status(f"第{account_index}个账号 {username} 退出检测超时 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"第{account_index}个账号等待退出异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"第{account_index}个账号 {username} 等待退出异常: {str(e)} - By @ConceptualGod")

    def _prepare_next_account(self, next_username: str):
        """
        准备下一个账号登录（清除上一个账号输入框内容）

        Args:
            next_username: 下一个账号用户名

        开发者: @ConceptualGod
        """
        try:
            # 使用记录的上一个账号名
            last_username = self.last_logged_username
            self._log_status(f"准备下一个账号: 清除上一个账号 {last_username}，准备输入 {next_username} - By @ConceptualGod")
            self._update_current_status("准备下一个账号...")

            # 等待界面稳定
            time.sleep(1)

            # 获取账号输入框坐标（login.json的第一步）
            if self.login_coordinates and len(self.login_coordinates) > 0:
                username_coord = self.login_coordinates[0]  # 第一步是账号输入框
                x = username_coord.get("x", 0)
                y = username_coord.get("y", 0)

                self._log_status(f"定位账号输入框 - 坐标({x}, {y}) - By @ConceptualGod")

                # 点击账号输入框
                pyautogui.click(x, y)
                time.sleep(0.5)

                # 清除上一个账号内容
                self._clear_username_input_with_backspace(last_username)

                self._log_status(f"账号输入框已清除，准备输入下一个账号 {next_username} - By @ConceptualGod")
            else:
                self._log_status("未找到账号输入框坐标配置 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"准备下一个账号异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"准备下一个账号异常: {str(e)} - By @ConceptualGod")

    def _clear_username_input_with_backspace(self, username: str):
        """
        使用退格键清除账号输入框内容

        Args:
            username: 要清除的用户名

        开发者: @ConceptualGod
        """
        try:
            self._log_status(f"开始使用退格键清除账号输入框内容: {username} - By @ConceptualGod")

            # 计算用户名长度
            username_length = len(username)
            self._log_status(f"账号 {username} 长度为 {username_length} 个字符 - By @ConceptualGod")

            # 使用退格键逐个删除字符
            total_backspace = username_length + 15  # 多删除15个字符确保完全清空
            for i in range(total_backspace):
                pyautogui.press('backspace')
                time.sleep(0.15)  # 每次按键间隔
                self.logger.debug(f"退格键第 {i+1} 次，剩余约 {max(0, username_length - i)} 个字符 - By @ConceptualGod")

            self._log_status(f"账号输入框内容已使用退格键清除完成 - By @ConceptualGod")

        except Exception as e:
            self.logger.error(f"使用退格键清除账号输入框异常 - By @ConceptualGod: {str(e)}")
            self._log_status(f"使用退格键清除账号输入框异常: {str(e)} - By @ConceptualGod")

    def _stop_login(self):
        """停止轮登"""
        self.is_running = False
        self._log_status("用户停止轮登")
        self._update_current_status("正在停止...")


    
    def _update_button_state(self):
        """更新按钮状态"""
        if self.is_running:
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
        else:
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def _update_current_status(self, status: str):
        """更新当前状态"""
        self.current_status_label.config(text=f"状态: {status}")
    
    def _log_status(self, message: str):
        """记录状态日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        # 更新状态文本框
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, log_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
        
        # 记录到日志文件
        self.logger.info(message)

    def _load_login_coordinates(self):
        """
        加载登录坐标配置
        开发者: @ConceptualGod
        """
        try:
            if os.path.exists(self.login_config_file):
                with open(self.login_config_file, 'r', encoding='utf-8') as f:
                    self.login_coordinates = json.load(f)

                self.logger.info(f"加载登录坐标配置成功，共 {len(self.login_coordinates)} 个坐标 - By @ConceptualGod")

                # 按步骤排序
                self.login_coordinates.sort(key=lambda x: x.get("step", 0))

            else:
                self.logger.warning(f"登录坐标配置文件不存在: {self.login_config_file} - By @ConceptualGod")
                self.login_coordinates = []

        except Exception as e:
            self.logger.error(f"加载登录坐标配置失败: {str(e)} - By @ConceptualGod")
            self.login_coordinates = []
