#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理模块
处理CSV和Excel文件的导入导出

开发者: @ConceptualGod
"""

import pandas as pd
import csv
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import tkinter.filedialog as filedialog
import tkinter.messagebox as messagebox

class FileHandler:
    """文件处理器"""
    
    def __init__(self):
        """初始化文件处理器"""
        self.logger = logging.getLogger(__name__)
        self.json_file_path = Path("data/accounts.json")

        # 确保data目录存在
        self.json_file_path.parent.mkdir(exist_ok=True)

        # 账号数据的基本字段
        self.required_fields = ['username', 'password']

        # 支持的列名映射 (中文 -> 英文)
        self.column_mapping = {
            '账号': 'username',
            '用户名': 'username',
            'username': 'username',
            '密码': 'password',
            'password': 'password'
        }
    
    def import_file(self) -> Optional[List[Dict[str, Any]]]:
        """
        导入文件对话框
        
        Returns:
            账号数据列表，失败返回None
        """
        try:
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择账号文件",
                filetypes=[
                    ("Excel文件", "*.xlsx *.xls"),
                    ("CSV文件", "*.csv"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not file_path:
                return None
            
            file_path = Path(file_path)
            self.logger.info(f"导入文件: {file_path}")
            
            # 根据文件扩展名处理
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                return self._import_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                return self._import_csv(file_path)
            else:
                messagebox.showerror("错误 - By @ConceptualGod", "不支持的文件格式")
                return None
                
        except Exception as e:
            self.logger.error(f"导入文件异常: {str(e)}")
            messagebox.showerror("错误 - By @ConceptualGod", f"导入文件失败: {str(e)}")
            return None
    
    def _import_excel(self, file_path: Path) -> Optional[List[Dict[str, Any]]]:
        """
        导入Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            账号数据列表
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 转换为账号数据
            new_accounts = self._dataframe_to_accounts(df)

            if new_accounts:
                # 加载现有账号
                existing_accounts = self.load_accounts_from_json()

                # 去重并合并
                merged_accounts, added_count, duplicate_count = self._merge_accounts(existing_accounts, new_accounts)

                # 保存为JSON文件
                self._save_accounts_to_json(merged_accounts)

                messagebox.showinfo("成功 - By @ConceptualGod", f"成功导入 {added_count} 个新账号，跳过 {duplicate_count} 个重复账号")

            return new_accounts
            
        except Exception as e:
            self.logger.error(f"导入Excel文件异常: {str(e)}")
            messagebox.showerror("错误", f"导入Excel文件失败: {str(e)} - By @ConceptualGod")
            return None
    
    def _import_csv(self, file_path: Path) -> Optional[List[Dict[str, Any]]]:
        """
        导入CSV文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            账号数据列表
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 转换为账号数据
            new_accounts = self._dataframe_to_accounts(df)

            if new_accounts:
                # 加载现有账号
                existing_accounts = self.load_accounts_from_json()

                # 去重并合并
                merged_accounts, added_count, duplicate_count = self._merge_accounts(existing_accounts, new_accounts)

                # 保存到本地JSON文件
                self._save_accounts_to_json(merged_accounts)

                messagebox.showinfo("成功 - By @ConceptualGod", f"成功导入 {added_count} 个新账号，跳过 {duplicate_count} 个重复账号")

            return new_accounts
            
        except Exception as e:
            self.logger.error(f"导入CSV文件异常: {str(e)}")
            messagebox.showerror("错误", f"导入CSV文件失败: {str(e)} - By @ConceptualGod")
            return None
    
    def _dataframe_to_accounts(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        将DataFrame转换为账号数据列表

        Args:
            df: pandas DataFrame

        Returns:
            账号数据列表
        """
        accounts = []

        # 映射列名 (支持中文列名)
        mapped_columns = {}
        for col in df.columns:
            if col in self.column_mapping:
                mapped_columns[self.column_mapping[col]] = col

        # 检查必要的列
        missing_fields = []
        for field in self.required_fields:
            if field not in mapped_columns:
                missing_fields.append(field)

        if missing_fields:
            # 提供友好的错误提示
            missing_names = []
            for field in missing_fields:
                if field == 'username':
                    missing_names.append("账号/用户名/username")
                elif field == 'password':
                    missing_names.append("密码/password")

            messagebox.showerror("错误 - By @ConceptualGod", f"缺少必要的列: {missing_names}\n\n当前文件包含的列: {list(df.columns)}")
            return []

        # 转换数据
        username_col = mapped_columns['username']
        password_col = mapped_columns['password']

        for _, row in df.iterrows():
            # 跳过空行
            if pd.isna(row.get(username_col)) or pd.isna(row.get(password_col)):
                continue

            # 只处理用户名和密码
            account = {
                'username': str(row.get(username_col, '')).strip(),
                'password': str(row.get(password_col, '')).strip()
            }

            # 验证账号数据（只验证用户名和密码）
            if account['username'] and account['password']:
                accounts.append(account)
        
        return accounts
    
    def load_accounts_from_json(self) -> List[Dict[str, Any]]:
        """
        从JSON文件加载账号数据

        Returns:
            账号数据列表
        """
        try:
            if not self.json_file_path.exists():
                # 创建空的JSON文件
                self._create_empty_json()
                return []

            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                accounts = json.load(f)

            # 确保每个账号都有必要的字段
            for account in accounts:
                for field in self.required_fields:
                    if field not in account:
                        account[field] = ''

            return accounts

        except Exception as e:
            self.logger.error(f"加载JSON文件异常: {str(e)}")
            return []
    
    def _save_accounts_to_json(self, accounts: List[Dict[str, Any]]):
        """
        保存账号数据到JSON文件

        Args:
            accounts: 账号数据列表
        """
        try:
            with open(self.json_file_path, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)
            self.logger.info(f"账号数据已保存到: {self.json_file_path}")

        except Exception as e:
            self.logger.error(f"保存JSON文件异常: {str(e)}")
            messagebox.showerror("错误", f"保存文件失败: {str(e)} - By @ConceptualGod")

    def save_accounts(self, accounts: List[Dict[str, Any]]):
        """
        保存账号数据

        Args:
            accounts: 账号数据列表
        """
        self._save_accounts_to_json(accounts)
    
    def _create_empty_json(self):
        """创建空的JSON文件"""
        try:
            with open(self.json_file_path, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"创建空JSON文件异常: {str(e)}")
    
    def export_accounts(self, accounts: List[Dict[str, Any]]):
        """
        导出账号数据
        
        Args:
            accounts: 账号数据列表
        """
        try:
            # 打开保存文件对话框
            file_path = filedialog.asksaveasfilename(
                title="导出账号数据",
                defaultextension=".csv",
                filetypes=[
                    ("CSV文件", "*.csv"),
                    ("Excel文件", "*.xlsx"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            file_path = Path(file_path)
            
            # 根据文件扩展名保存
            if file_path.suffix.lower() == '.csv':
                df = pd.DataFrame(accounts)
                df.to_csv(file_path, index=False, encoding='utf-8')
            elif file_path.suffix.lower() == '.xlsx':
                df = pd.DataFrame(accounts)
                df.to_excel(file_path, index=False)
            
            messagebox.showinfo("成功", f"账号数据已导出到: {file_path} - By @ConceptualGod")
            
        except Exception as e:
            self.logger.error(f"导出文件异常: {str(e)}")
            messagebox.showerror("错误", f"导出文件失败: {str(e)} - By @ConceptualGod")
    
    def get_json_file_path(self) -> Path:
        """获取JSON文件路径"""
        return self.json_file_path

    def _merge_accounts(self, existing_accounts: List[Dict[str, Any]],
                       new_accounts: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], int, int]:
        """
        合并账号列表，去除重复账号

        Args:
            existing_accounts: 现有账号列表
            new_accounts: 新账号列表

        Returns:
            (合并后的账号列表, 新增账号数量, 重复账号数量)
        """
        # 创建现有账号的用户名集合，用于快速查重
        existing_usernames = {acc['username'] for acc in existing_accounts}

        merged_accounts = existing_accounts.copy()
        added_count = 0
        duplicate_count = 0

        for new_account in new_accounts:
            username = new_account['username']

            if username in existing_usernames:
                # 重复账号，跳过
                duplicate_count += 1
                self.logger.info(f"跳过重复账号: {username}")
            else:
                # 新账号，添加到列表
                merged_accounts.append(new_account)
                existing_usernames.add(username)
                added_count += 1
                self.logger.info(f"添加新账号: {username}")

        return merged_accounts, added_count, duplicate_count

    def check_account_exists(self, username: str) -> bool:
        """
        检查账号是否已存在

        Args:
            username: 用户名

        Returns:
            是否存在
        """
        accounts = self.load_accounts_from_json()
        return any(acc['username'] == username for acc in accounts)

    def add_single_account(self, account: Dict[str, Any]) -> bool:
        """
        添加单个账号

        Args:
            account: 账号信息

        Returns:
            是否添加成功
        """
        try:
            if self.check_account_exists(account['username']):
                messagebox.showwarning("警告", f"账号 {account['username']} 已存在 - By @ConceptualGod")
                return False

            accounts = self.load_accounts_from_json()
            accounts.append(account)
            self._save_accounts_to_json(accounts)

            self.logger.info(f"成功添加账号: {account['username']} - By @ConceptualGod")
            return True

        except Exception as e:
            self.logger.error(f"添加账号异常: {str(e)}")
            messagebox.showerror("错误", f"添加账号失败: {str(e)} - By @ConceptualGod")
            return False
