b#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英雄数据分析工具
开发者: @ConceptualGod
"""

import json
import os

def analyze_hero_data():
    """
    分析英雄数据结构

    开发者: @ConceptualGod
    """
    try:
        # 读取英雄数据文件
        hero_file = os.path.join('..', '7fgame', 'Data', 'QXMyHeroInfo.json')

        with open(hero_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("英雄数据分析 - By @ConceptualGod")
        print("=" * 50)

        # 目标英雄（六个）
        target_heroes = ['华佗', '刘备', '诸葛瑾', '陆逊', '孙权', '曹操']

        # 分析数据结构
        if 'HeroData' in data:
            heroes = data['HeroData']
            print(f"发现 {len(heroes)} 个英雄")
            print(f"\n查找目标英雄: {', '.join(target_heroes)}")
            print("\n目标英雄信息:")

            found_heroes = {}
            for hero in heroes:
                hero_id = hero.get('heroId', 'N/A')
                hero_name = hero.get('heroName', 'N/A')
                country = hero.get('country', 'N/A')

                if hero_name in target_heroes:
                    found_heroes[hero_name] = {
                        'id': hero_id,
                        'name': hero_name,
                        'country': country
                    }
                    print(f"  {hero_name}: ID={hero_id}, 国家={country}")

            # 显示国家分类
            print("\n国家分类:")
            countries = {1: '蜀国', 2: '魏国', 3: '吴国', 0: '中立'}
            for country_id, country_name in countries.items():
                heroes_in_country = [h['name'] for h in found_heroes.values() if h['country'] == country_id]
                if heroes_in_country:
                    print(f"  {country_name}: {', '.join(heroes_in_country)}")

            return found_heroes

        return None

    except Exception as e:
        print(f"分析失败 - By @ConceptualGod: {str(e)}")
        return None

if __name__ == "__main__":
    analyze_hero_data()
