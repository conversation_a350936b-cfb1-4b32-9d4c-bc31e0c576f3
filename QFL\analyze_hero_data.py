#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英雄数据分析工具
开发者: @ConceptualGod
"""

import json
import os

def analyze_hero_data():
    """
    分析英雄数据结构
    
    开发者: @ConceptualGod
    """
    try:
        # 读取英雄数据文件
        hero_file = os.path.join('..', '7fgame', 'Data', 'QXMyHeroInfo.json')
        
        with open(hero_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("英雄数据分析 - By @ConceptualGod")
        print("=" * 50)
        
        # 分析数据结构
        if 'HeroData' in data:
            heroes = data['HeroData']
            print(f"发现 {len(heroes)} 个英雄")
            print("\n英雄列表:")
            
            for i, hero in enumerate(heroes[:10]):  # 只显示前10个
                hero_id = hero.get('heroId', 'N/A')
                hero_name = hero.get('heroName', 'N/A')
                country = hero.get('country', 'N/A')
                print(f"{i+1}. ID:{hero_id} 名称:{hero_name} 国家:{country}")
                
                # 查找目标英雄
                target_heroes = ['张飞', '关羽', '赵云', '华佗', '刘备', '诸葛瑾', '陆逊', '孙权', '曹操']
                if hero_name in target_heroes:
                    print(f"   *** 找到目标英雄: {hero_name} ***")
        
        # 查找战功相关数据
        if 'zhangong' in str(data).lower():
            print("\n发现战功相关数据")
            
        return data
        
    except Exception as e:
        print(f"分析失败 - By @ConceptualGod: {str(e)}")
        return None

if __name__ == "__main__":
    analyze_hero_data()
