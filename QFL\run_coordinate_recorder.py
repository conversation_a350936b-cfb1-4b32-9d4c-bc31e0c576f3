#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标录制器启动脚本
开发者: @ConceptualGod
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from coordinate_recorder import main
    
    if __name__ == "__main__":
        print("=" * 50)
        print("坐标录制器 v1.0")
        print("开发者: @ConceptualGod")
        print("=" * 50)
        print()
        print("使用说明:")
        print("1. Ctrl+R: 开始/停止录制 - By @ConceptualGod")
        print("2. Ctrl+Space: 记录当前鼠标坐标")
        print("3. 双击列表项可编辑坐标")
        print("4. 支持加载/保存JSON格式坐标文件")
        print("5. 窗口始终置顶，方便在游戏中使用")
        print()
        print("启动中... - By @ConceptualGod")
        print()
        
        main()
        
except ImportError as e:
    print(f"导入模块失败: {e} - By @ConceptualGod")
    print("请确保所有依赖包已安装:")
    print("pip install pyautogui keyboard")
    input("按回车键退出...")
    
except Exception as e:
    print(f"启动失败: {e} - By @ConceptualGod")
    input("按回车键退出...")
