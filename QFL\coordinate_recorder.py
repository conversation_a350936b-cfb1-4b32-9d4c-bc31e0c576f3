#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标录制器
独立的GUI工具，用于在游戏中记录坐标

开发者: @ConceptualGod
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import time
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional
import pyautogui
import keyboard
import mouse
import os
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class CoordinateRecorder:
    """
    坐标录制器主类
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化坐标录制器
        开发者: @ConceptualGod
        """
        # 配置文件路径
        self.config_file = "coordinate_recorder_config.json"

        # 加载配置
        self.load_config()

        # 窗口透明度设置 - 必须在创建窗口前设置
        self.alpha_value = getattr(self, 'alpha_value', 0.85)  # 默认透明度85%

        self.root = tk.Tk()
        self.setup_window()

        # 数据存储
        self.coordinates: List[Dict[str, Any]] = []
        self.is_recording = False
        self.is_testing = False
        self.current_step = 1

        # 重新记录模式
        self.rerecord_mode = False
        self.rerecord_data = None

        # 快捷键设置
        self.record_hotkey = 'ctrl+r'  # 开始/停止录制
        self.capture_hotkey = 'ctrl+space'  # 记录当前坐标
        
        # 创建界面
        self.create_widgets()
        
        # 设置快捷键监听
        self.setup_hotkeys()
        
        print("坐标录制器启动完成 - By @ConceptualGod")
        print(f"快捷键: {self.record_hotkey} = 开始/停止录制 - By @ConceptualGod")
        print(f"快捷键: {self.capture_hotkey} = 记录坐标")
    
    def setup_window(self):
        """
        设置窗口属性
        开发者: @ConceptualGod
        """
        self.root.title("坐标录制器 - By @ConceptualGod")
        self.root.geometry("800x700")  # 增加高度以显示所有按钮

        # 设置窗口置顶
        self.root.attributes('-topmost', True)

        # 设置窗口半透明 (0.0完全透明, 1.0完全不透明)
        self.root.attributes('-alpha', self.alpha_value)

        # 设置窗口图标
        self._set_window_icon()

        # 设置窗口图标和样式
        self.root.configure(bg='#f0f0f0')

        # 禁止调整窗口大小
        self.root.resizable(False, False)

        print("窗口设置完成 - 半透明度: 85% - By @ConceptualGod")
    
    def create_widgets(self):
        """
        创建界面组件
        开发者: @ConceptualGod
        """
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="坐标录制器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 控制面板
        self.create_control_panel(main_frame)
        
        # 坐标列表
        self.create_coordinate_list(main_frame)
        
        # 操作按钮
        self.create_action_buttons(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
        
        # 开发者签名
        signature_frame = ttk.Frame(main_frame)
        signature_frame.pack(fill=tk.X, pady=(10, 0))
        signature_label = ttk.Label(signature_frame, text="By @ConceptualGod",
                                   foreground="gray", font=("Arial", 8))
        signature_label.pack(side=tk.RIGHT)
    
    def create_control_panel(self, parent):
        """
        创建控制面板
        开发者: @ConceptualGod
        """
        control_frame = ttk.LabelFrame(parent, text="录制控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 录制状态
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(status_frame, text="录制状态:").pack(side=tk.LEFT)
        self.status_label = ttk.Label(status_frame, text="未录制", 
                                     foreground="red", font=("Arial", 10, "bold"))
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 快捷键说明
        hotkey_frame = ttk.Frame(control_frame)
        hotkey_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(hotkey_frame, text="快捷键:").pack(side=tk.LEFT)
        ttk.Label(hotkey_frame, text=f"{self.record_hotkey} = 开始/停止录制").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(hotkey_frame, text=f"{self.capture_hotkey} = 记录坐标").pack(side=tk.LEFT, padx=(10, 0))
        
        # 手动控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.record_button = ttk.Button(button_frame, text="开始录制", 
                                       command=self.toggle_recording)
        self.record_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.capture_button = ttk.Button(button_frame, text="记录坐标",
                                        command=self.capture_coordinate,
                                        state=tk.DISABLED)
        self.capture_button.pack(side=tk.LEFT, padx=(0, 5))

        # 透明度控制
        alpha_frame = ttk.Frame(control_frame)
        alpha_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(alpha_frame, text="透明度:").pack(side=tk.LEFT)

        # 透明度滑块
        self.alpha_var = tk.DoubleVar(value=self.alpha_value)
        alpha_scale = ttk.Scale(alpha_frame, from_=0.3, to=1.0,
                               variable=self.alpha_var, orient=tk.HORIZONTAL,
                               length=200, command=self.on_alpha_change)
        alpha_scale.pack(side=tk.LEFT, padx=(10, 5))

        # 透明度数值显示
        self.alpha_label = ttk.Label(alpha_frame, text=f"{int(self.alpha_value * 100)}%")
        self.alpha_label.pack(side=tk.LEFT, padx=(5, 0))

        # 透明度预设按钮
        ttk.Button(alpha_frame, text="50%", width=5,
                  command=lambda: self.set_alpha(0.5)).pack(side=tk.LEFT, padx=(10, 2))
        ttk.Button(alpha_frame, text="75%", width=5,
                  command=lambda: self.set_alpha(0.75)).pack(side=tk.LEFT, padx=(2, 2))
        ttk.Button(alpha_frame, text="85%", width=5,
                  command=lambda: self.set_alpha(0.85)).pack(side=tk.LEFT, padx=(2, 2))
        ttk.Button(alpha_frame, text="100%", width=5,
                  command=lambda: self.set_alpha(1.0)).pack(side=tk.LEFT, padx=(2, 0))
    
    def create_coordinate_list(self, parent):
        """
        创建坐标列表
        开发者: @ConceptualGod
        """
        list_frame = ttk.LabelFrame(parent, text="坐标列表")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview
        columns = ("步骤", "X坐标", "Y坐标", "说明", "时间", "状态")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, anchor=tk.CENTER)
        
        # 设置说明列宽度
        self.tree.column("说明", width=200)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self.edit_coordinate)
    
    def create_action_buttons(self, parent):
        """
        创建操作按钮
        开发者: @ConceptualGod
        """
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件操作
        ttk.Button(button_frame, text="加载坐标", 
                  command=self.load_coordinates).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存坐标", 
                  command=self.save_coordinates).pack(side=tk.LEFT, padx=(0, 5))
        
        # 编辑操作
        ttk.Button(button_frame, text="编辑选中", 
                  command=self.edit_selected).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(button_frame, text="删除选中", 
                  command=self.delete_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清除全部", 
                  command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        
        # 测试操作
        ttk.Button(button_frame, text="测试选中",
                  command=self.test_click).pack(side=tk.RIGHT, padx=(5, 0))

        self.test_all_button = ttk.Button(button_frame, text="全部测试",
                                         command=self.test_all_coordinates)
        self.test_all_button.pack(side=tk.RIGHT, padx=(5, 0))

        self.stop_test_button = ttk.Button(button_frame, text="停止测试",
                                          command=self.stop_testing,
                                          state=tk.DISABLED)
        self.stop_test_button.pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_status_bar(self, parent):
        """
        创建状态栏
        开发者: @ConceptualGod
        """
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        self.status_text = tk.StringVar()
        self.status_text.set("就绪 - By @ConceptualGod")
        
        status_label = ttk.Label(status_frame, textvariable=self.status_text)
        status_label.pack(side=tk.LEFT)
        
        # 坐标计数
        self.count_text = tk.StringVar()
        self.count_text.set("坐标数量: 0")
        
        count_label = ttk.Label(status_frame, textvariable=self.count_text)
        count_label.pack(side=tk.RIGHT)

    def setup_hotkeys(self):
        """
        设置快捷键监听
        开发者: @ConceptualGod
        """
        try:
            # 注册快捷键
            keyboard.add_hotkey(self.record_hotkey, self.toggle_recording)
            keyboard.add_hotkey(self.capture_hotkey, self.capture_coordinate)

            self.update_status("快捷键已注册 - By @ConceptualGod")

        except Exception as e:
            self.update_status(f"快捷键注册失败: {str(e)} - By @ConceptualGod")

    def toggle_recording(self):
        """
        切换录制状态
        开发者: @ConceptualGod
        """
        try:
            self.is_recording = not self.is_recording

            if self.is_recording:
                self.status_label.config(text="录制中", foreground="green")
                self.record_button.config(text="停止录制")
                self.capture_button.config(state=tk.NORMAL)
                self.update_status("开始录制坐标 - By @ConceptualGod")
            else:
                self.status_label.config(text="未录制", foreground="red")
                self.record_button.config(text="开始录制")
                self.capture_button.config(state=tk.DISABLED)
                self.update_status("停止录制坐标 - By @ConceptualGod")

        except Exception as e:
            self.update_status(f"切换录制状态失败: {str(e)} - By @ConceptualGod")

    def on_alpha_change(self, value):
        """
        透明度滑块变化回调

        Args:
            value: 透明度值

        开发者: @ConceptualGod
        """
        try:
            alpha = float(value)
            self.alpha_value = alpha

            # 更新窗口透明度
            self.root.attributes('-alpha', alpha)

            # 更新显示标签
            self.alpha_label.config(text=f"{int(alpha * 100)}%")

            self.update_status(f"透明度已调整为 {int(alpha * 100)}% - By @ConceptualGod")

            # 自动保存配置
            self.save_config()

        except Exception as e:
            self.update_status(f"调整透明度失败: {str(e)} - By @ConceptualGod")

    def set_alpha(self, alpha: float):
        """
        设置指定透明度

        Args:
            alpha: 透明度值 (0.0-1.0)

        开发者: @ConceptualGod
        """
        try:
            self.alpha_value = alpha
            self.alpha_var.set(alpha)

            # 更新窗口透明度
            self.root.attributes('-alpha', alpha)

            # 更新显示标签
            self.alpha_label.config(text=f"{int(alpha * 100)}%")

            self.update_status(f"透明度设置为 {int(alpha * 100)}% - By @ConceptualGod")

            # 自动保存配置
            self.save_config()

        except Exception as e:
            self.update_status(f"设置透明度失败: {str(e)} - By @ConceptualGod")

    def capture_coordinate(self):
        """
        记录当前鼠标坐标
        开发者: @ConceptualGod
        """
        try:
            if not self.is_recording:
                self.update_status("请先开始录制 - By @ConceptualGod")
                return

            # 获取当前鼠标位置
            x, y = pyautogui.position()

            # 检查是否为重新记录模式
            if self.rerecord_mode and self.rerecord_data:
                # 重新记录模式：更新现有坐标
                self.handle_rerecord(x, y)
                return

            # 正常记录模式：弹出说明输入对话框
            description = self.get_description_input(x, y)
            if description is None:  # 用户取消
                return

            # 创建坐标记录
            coordinate = {
                "step": self.current_step,
                "x": x,
                "y": y,
                "description": description,
                "timestamp": datetime.now().strftime("%H:%M:%S"),
                "status": "未执行"
            }

            # 添加到列表
            self.coordinates.append(coordinate)
            self.current_step += 1

            # 更新界面
            self.refresh_coordinate_list()
            self.update_status(f"记录坐标: ({x}, {y}) - {description} - By @ConceptualGod")

        except Exception as e:
            self.update_status(f"记录坐标失败: {str(e)} - By @ConceptualGod")

    def handle_rerecord(self, x: int, y: int):
        """
        处理重新记录坐标

        Args:
            x: 新的X坐标
            y: 新的Y坐标

        开发者: @ConceptualGod
        """
        try:
            # 获取重新记录数据
            rerecord_data = self.rerecord_data
            x_var = rerecord_data['x_var']
            y_var = rerecord_data['y_var']
            dialog = rerecord_data['dialog']

            # 更新坐标变量
            x_var.set(str(x))
            y_var.set(str(y))

            # 恢复编辑对话框
            dialog.deiconify()
            dialog.lift()
            dialog.focus_force()

            # 清除重新记录模式
            self.rerecord_mode = False
            self.rerecord_data = None

            # 更新状态
            self.update_status(f"重新记录坐标成功: ({x}, {y}) - By @ConceptualGod")

        except Exception as e:
            self.update_status(f"处理重新记录失败: {str(e)} - By @ConceptualGod")
            # 清除重新记录模式
            self.rerecord_mode = False
            self.rerecord_data = None

    def get_description_input(self, x: int, y: int) -> Optional[str]:
        """
        获取坐标说明输入

        Args:
            x: X坐标
            y: Y坐标

        Returns:
            用户输入的说明，如果取消则返回None

        开发者: @ConceptualGod
        """
        # 创建输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("输入坐标说明")
        dialog.geometry("400x200")
        dialog.attributes('-topmost', True)
        dialog.resizable(False, False)

        # 居中显示
        dialog.transient(self.root)
        dialog.grab_set()

        result = {"description": None}

        # 坐标信息
        info_frame = ttk.Frame(dialog)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(info_frame, text=f"坐标: ({x}, {y})",
                 font=("Arial", 12, "bold")).pack()

        # 输入框
        input_frame = ttk.Frame(dialog)
        input_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(input_frame, text="说明:").pack(anchor=tk.W)

        description_var = tk.StringVar()
        description_entry = ttk.Entry(input_frame, textvariable=description_var, width=50)
        description_entry.pack(fill=tk.X, pady=(5, 0))
        description_entry.focus()

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        def on_ok():
            result["description"] = description_var.get().strip()
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.RIGHT)

        # 绑定回车键
        description_entry.bind('<Return>', lambda e: on_ok())

        # 等待对话框关闭
        dialog.wait_window()

        return result["description"] if result["description"] else None

    def refresh_coordinate_list(self):
        """
        刷新坐标列表显示
        开发者: @ConceptualGod
        """
        try:
            # 清空现有项目
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 添加坐标项目
            for coord in self.coordinates:
                self.tree.insert("", tk.END, values=(
                    coord["step"],
                    coord["x"],
                    coord["y"],
                    coord["description"],
                    coord["timestamp"],
                    coord["status"]
                ))

            # 更新计数
            self.count_text.set(f"坐标数量: {len(self.coordinates)}")

        except Exception as e:
            self.update_status(f"刷新列表失败: {str(e)} - By @ConceptualGod")

    def load_coordinates(self):
        """
        加载坐标文件
        开发者: @ConceptualGod
        """
        try:
            file_path = filedialog.askopenfilename(
                title="选择坐标文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialdir="."
            )

            if not file_path:
                return

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if isinstance(data, list):
                self.coordinates = data
                # 更新步骤计数器
                if self.coordinates:
                    self.current_step = max(coord.get("step", 0) for coord in self.coordinates) + 1
                else:
                    self.current_step = 1

                self.refresh_coordinate_list()
                self.update_status(f"加载坐标文件成功: {len(self.coordinates)} 个坐标 - By @ConceptualGod")
            else:
                messagebox.showerror("错误", "文件格式不正确 - By @ConceptualGod")

        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"加载文件失败: {str(e)} - By @ConceptualGod")

    def save_coordinates(self):
        """
        保存坐标文件
        开发者: @ConceptualGod
        """
        try:
            if not self.coordinates:
                messagebox.showwarning("警告", "没有坐标数据可保存 - By @ConceptualGod")
                return

            file_path = filedialog.asksaveasfilename(
                title="保存坐标文件",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialdir="."
            )

            if not file_path:
                return

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.coordinates, f, ensure_ascii=False, indent=2)

            self.update_status(f"保存坐标文件成功: {len(self.coordinates)} 个坐标 - By @ConceptualGod")
            messagebox.showinfo("成功", f"坐标文件已保存到: {file_path} - By @ConceptualGod")

        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"保存文件失败: {str(e)} - By @ConceptualGod")

    def edit_selected(self):
        """
        编辑选中的坐标
        开发者: @ConceptualGod
        """
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要编辑的坐标 - By @ConceptualGod")
                return

            item = selection[0]
            values = self.tree.item(item, "values")

            if not values:
                return

            # 找到对应的坐标数据
            step = int(values[0])
            coord_index = None
            for i, coord in enumerate(self.coordinates):
                if coord["step"] == step:
                    coord_index = i
                    break

            if coord_index is None:
                messagebox.showerror("错误", "找不到对应的坐标数据 - By @ConceptualGod")
                return

            # 打开编辑对话框
            self.edit_coordinate_dialog(coord_index)

        except Exception as e:
            messagebox.showerror("错误", f"编辑坐标失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"编辑坐标失败: {str(e)} - By @ConceptualGod")

    def edit_coordinate(self, event):
        """
        双击编辑坐标
        开发者: @ConceptualGod
        """
        self.edit_selected()

    def edit_coordinate_dialog(self, coord_index: int):
        """
        坐标编辑对话框

        Args:
            coord_index: 坐标在列表中的索引

        开发者: @ConceptualGod
        """
        coord = self.coordinates[coord_index]

        # 创建编辑对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑坐标")
        dialog.geometry("400x300")
        dialog.attributes('-topmost', True)
        dialog.resizable(False, False)

        # 居中显示
        dialog.transient(self.root)
        dialog.grab_set()

        # 输入框架
        input_frame = ttk.Frame(dialog)
        input_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # X坐标
        ttk.Label(input_frame, text="X坐标:").grid(row=0, column=0, sticky=tk.W, pady=5)
        x_var = tk.StringVar(value=str(coord["x"]))
        x_entry = ttk.Entry(input_frame, textvariable=x_var, width=20)
        x_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Y坐标
        ttk.Label(input_frame, text="Y坐标:").grid(row=1, column=0, sticky=tk.W, pady=5)
        y_var = tk.StringVar(value=str(coord["y"]))
        y_entry = ttk.Entry(input_frame, textvariable=y_var, width=20)
        y_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 说明
        ttk.Label(input_frame, text="说明:").grid(row=2, column=0, sticky=tk.W, pady=5)
        desc_var = tk.StringVar(value=coord["description"])
        desc_entry = ttk.Entry(input_frame, textvariable=desc_var, width=40)
        desc_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 状态
        ttk.Label(input_frame, text="状态:").grid(row=3, column=0, sticky=tk.W, pady=5)
        status_var = tk.StringVar(value=coord["status"])
        status_combo = ttk.Combobox(input_frame, textvariable=status_var,
                                   values=["未执行", "已执行", "跳过"], width=17)
        status_combo.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        def on_save():
            try:
                # 验证输入
                x = int(x_var.get())
                y = int(y_var.get())
                description = desc_var.get().strip()
                status = status_var.get()

                if not description:
                    messagebox.showwarning("警告", "请输入说明 - By @ConceptualGod")
                    return

                # 更新坐标数据
                self.coordinates[coord_index].update({
                    "x": x,
                    "y": y,
                    "description": description,
                    "status": status
                })

                # 刷新列表
                self.refresh_coordinate_list()
                self.update_status(f"坐标编辑成功 - By @ConceptualGod")
                dialog.destroy()

            except ValueError:
                messagebox.showerror("错误", "坐标必须是数字 - By @ConceptualGod")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)} - By @ConceptualGod")

        def on_rerecord():
            """重新记录坐标"""
            try:
                # 检查是否正在录制状态
                if not self.is_recording:
                    messagebox.showwarning("提示", "请先开启录制状态，然后再使用重新记录功能")
                    return

                # 隐藏对话框
                dialog.withdraw()

                # 显示快捷键提示
                messagebox.showinfo("重新记录坐标",
                                  f"当前编辑: {desc_var.get()}\n"
                                  f"请按快捷键 {self.capture_hotkey} 记录新坐标\n"
                                  f"记录完成后编辑对话框将自动更新")

                # 设置重新记录模式
                self.start_rerecord_mode(coord_index, x_var, y_var, dialog)

            except Exception as e:
                messagebox.showerror("错误", f"重新记录失败: {str(e)} - By @ConceptualGod")
                dialog.deiconify()  # 恢复对话框显示

        def on_cancel():
            dialog.destroy()

        # 按钮布局：重新记录、保存、取消
        ttk.Button(button_frame, text="重新记录", command=on_rerecord).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="保存", command=on_save).pack(side=tk.RIGHT, padx=(5, 0))

    def start_rerecord_mode(self, coord_index: int, x_var: tk.StringVar, y_var: tk.StringVar, dialog: tk.Toplevel):
        """
        开始重新记录模式，等待用户按快捷键记录新坐标

        Args:
            coord_index: 要编辑的坐标索引
            x_var: X坐标变量
            y_var: Y坐标变量
            dialog: 编辑对话框

        开发者: @ConceptualGod
        """
        try:
            # 设置重新记录标记
            self.rerecord_mode = True
            self.rerecord_data = {
                'coord_index': coord_index,
                'x_var': x_var,
                'y_var': y_var,
                'dialog': dialog
            }

            # 更新状态
            self.update_status(f"重新记录模式：请按 {self.capture_hotkey} 记录新坐标 - By @ConceptualGod")

        except Exception as e:
            self.update_status(f"开始重新记录模式失败: {str(e)} - By @ConceptualGod")
            dialog.deiconify()

    def delete_selected(self):
        """
        删除选中的坐标
        开发者: @ConceptualGod
        """
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要删除的坐标 - By @ConceptualGod")
                return

            # 确认删除
            if not messagebox.askyesno("确认", "确定要删除选中的坐标吗？"):
                return

            # 获取要删除的步骤号
            steps_to_delete = []
            for item in selection:
                values = self.tree.item(item, "values")
                if values:
                    steps_to_delete.append(int(values[0]))

            # 从数据中删除
            self.coordinates = [coord for coord in self.coordinates
                              if coord["step"] not in steps_to_delete]

            # 重新编号
            for i, coord in enumerate(self.coordinates):
                coord["step"] = i + 1

            self.current_step = len(self.coordinates) + 1

            # 刷新列表
            self.refresh_coordinate_list()
            self.update_status(f"删除 {len(steps_to_delete)} 个坐标 - By @ConceptualGod")

        except Exception as e:
            messagebox.showerror("错误", f"删除坐标失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"删除坐标失败: {str(e)} - By @ConceptualGod")

    def clear_all(self):
        """
        清除所有坐标
        开发者: @ConceptualGod
        """
        try:
            if not self.coordinates:
                messagebox.showinfo("提示", "没有坐标数据可清除")
                return

            # 确认清除
            if not messagebox.askyesno("确认", f"确定要清除所有 {len(self.coordinates)} 个坐标吗？"):
                return

            # 清除数据
            self.coordinates.clear()
            self.current_step = 1

            # 刷新列表
            self.refresh_coordinate_list()
            self.update_status("已清除所有坐标 - By @ConceptualGod")

        except Exception as e:
            messagebox.showerror("错误", f"清除坐标失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"清除坐标失败: {str(e)} - By @ConceptualGod")

    def test_click(self):
        """
        测试点击选中的坐标
        开发者: @ConceptualGod
        """
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要测试的坐标 - By @ConceptualGod")
                return

            item = selection[0]
            values = self.tree.item(item, "values")

            if not values:
                return

            x = int(values[1])
            y = int(values[2])
            description = values[3]
            step = int(values[0])

            # 确认测试
            if not messagebox.askyesno("确认", f"确定要测试点击坐标 ({x}, {y}) - {description} 吗？\n\n点击确定后将在3秒后执行点击"):
                return

            # 倒计时
            for i in range(3, 0, -1):
                self.update_status(f"测试点击倒计时: {i} 秒 - By @ConceptualGod")
                self.root.update()
                time.sleep(1)

            # 执行点击
            pyautogui.click(x, y)

            # 更新坐标状态为已执行
            for coord in self.coordinates:
                if coord["step"] == step:
                    coord["status"] = "已执行"
                    break

            # 刷新列表显示
            self.refresh_coordinate_list()

            self.update_status(f"测试点击完成: ({x}, {y}) - {description} - By @ConceptualGod")

        except Exception as e:
            messagebox.showerror("错误", f"测试点击失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"测试点击失败: {str(e)} - By @ConceptualGod")

    def test_all_coordinates(self):
        """
        测试所有坐标点击
        开发者: @ConceptualGod
        """
        try:
            if not self.coordinates:
                messagebox.showwarning("警告", "没有坐标数据可测试 - By @ConceptualGod")
                return

            # 确认测试
            result = messagebox.askyesno(
                "确认批量测试",
                f"确定要依次测试所有 {len(self.coordinates)} 个坐标吗？\n\n"
                f"测试将按照步骤顺序执行，每个坐标间隔2秒。\n"
                f"点击确定后将在3秒后开始执行。"
            )

            if not result:
                return

            # 开始倒计时
            for i in range(3, 0, -1):
                self.update_status(f"批量测试倒计时: {i} 秒 - By @ConceptualGod")
                self.root.update()
                time.sleep(1)

            # 设置测试状态
            self.is_testing = True
            self.test_all_button.config(state=tk.DISABLED)
            self.stop_test_button.config(state=tk.NORMAL)

            # 执行批量测试
            self.update_status("开始批量测试坐标 - By @ConceptualGod")

            # 按步骤顺序排序
            sorted_coordinates = sorted(self.coordinates, key=lambda x: x.get("step", 0))

            for i, coord in enumerate(sorted_coordinates):
                if not self.is_testing:
                    # 如果测试被中断，退出
                    self.update_status("批量测试已中断 - By @ConceptualGod")
                    break

                x = coord["x"]
                y = coord["y"]
                description = coord["description"]
                step = coord["step"]

                # 更新状态
                self.update_status(f"测试第 {step} 步: {description} ({x}, {y}) - By @ConceptualGod")
                self.root.update()

                # 执行点击
                pyautogui.click(x, y)

                # 更新坐标状态为已执行
                coord["status"] = "已执行"

                # 刷新列表显示
                self.refresh_coordinate_list()

                # 如果不是最后一个坐标，等待间隔时间
                if i < len(sorted_coordinates) - 1:
                    for remaining in range(2, 0, -1):
                        self.update_status(f"等待 {remaining} 秒后执行下一个坐标... - By @ConceptualGod")
                        self.root.update()
                        time.sleep(1)

            self.update_status(f"批量测试完成，共执行 {len(sorted_coordinates)} 个坐标 - By @ConceptualGod")
            messagebox.showinfo("测试完成", f"批量测试已完成！\n共执行了 {len(sorted_coordinates)} 个坐标点击。 - By @ConceptualGod")

        except Exception as e:
            messagebox.showerror("错误", f"批量测试失败: {str(e)} - By @ConceptualGod")
            self.update_status(f"批量测试失败: {str(e)} - By @ConceptualGod")
        finally:
            # 恢复按钮状态
            self.is_testing = False
            self.test_all_button.config(state=tk.NORMAL)
            self.stop_test_button.config(state=tk.DISABLED)

    def stop_testing(self):
        """
        停止批量测试
        开发者: @ConceptualGod
        """
        try:
            if self.is_testing:
                self.is_testing = False
                self.update_status("用户中断批量测试 - By @ConceptualGod")

                # 恢复按钮状态
                self.test_all_button.config(state=tk.NORMAL)
                self.stop_test_button.config(state=tk.DISABLED)

                messagebox.showinfo("测试中断", "批量测试已被用户中断")

        except Exception as e:
            self.update_status(f"停止测试失败: {str(e)} - By @ConceptualGod")

    def update_status(self, message: str):
        """
        更新状态栏

        Args:
            message: 状态消息

        开发者: @ConceptualGod
        """
        try:
            self.status_text.set(message)
            self.root.update_idletasks()
            print(message)  # 同时输出到控制台

        except Exception as e:
            print(f"更新状态失败: {str(e)} - By @ConceptualGod")

    def run(self):
        """
        运行坐标录制器
        开发者: @ConceptualGod
        """
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()

        except Exception as e:
            print(f"运行坐标录制器失败: {str(e)} - By @ConceptualGod")

    def on_closing(self):
        """
        关闭程序时的处理
        开发者: @ConceptualGod
        """
        try:
            # 如果有未保存的数据，提示用户
            if self.coordinates:
                result = messagebox.askyesnocancel(
                    "保存提醒",
                    f"检测到 {len(self.coordinates)} 个坐标数据未保存。\n\n是否要保存后退出？\n\n是=保存后退出，否=直接退出，取消=继续使用"
                )

                if result is True:  # 用户选择"是" - 保存后退出
                    if self.save_coordinates_on_exit():
                        self.cleanup()
                        self.root.destroy()
                elif result is False:  # 用户选择"否" - 直接退出
                    self.cleanup()
                    self.root.destroy()
                # result is None 表示用户选择"取消" - 不退出

            else:
                self.cleanup()
                self.root.destroy()

        except Exception as e:
            print(f"关闭程序失败: {str(e)} - By @ConceptualGod")
            self.root.destroy()

    def save_coordinates_on_exit(self) -> bool:
        """
        退出时保存坐标

        Returns:
            是否保存成功

        开发者: @ConceptualGod
        """
        try:
            # 生成默认文件名
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"coordinates_{timestamp}.json"

            file_path = filedialog.asksaveasfilename(
                title="保存坐标文件",
                defaultextension=".json",
                initialfilename=default_filename,
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialdir="."
            )

            if not file_path:
                return False  # 用户取消保存

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.coordinates, f, ensure_ascii=False, indent=2)

            self.update_status(f"坐标已保存到: {file_path} - By @ConceptualGod")
            return True

        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {str(e)} - By @ConceptualGod")
            return False

    def cleanup(self):
        """
        清理资源
        开发者: @ConceptualGod
        """
        try:
            # 移除快捷键监听
            keyboard.unhook_all()
            print("坐标录制器已关闭 - By @ConceptualGod")

        except Exception as e:
            print(f"清理资源失败: {str(e)} - By @ConceptualGod")

    def load_config(self):
        """
        加载配置文件
        开发者: @ConceptualGod
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载透明度设置
                self.alpha_value = config.get('alpha_value', 0.85)

                print(f"配置加载成功 - 透明度: {int(self.alpha_value * 100)}% - By @ConceptualGod")
            else:
                # 使用默认配置
                self.alpha_value = 0.85
                print("使用默认配置 - By @ConceptualGod")

        except Exception as e:
            print(f"加载配置失败，使用默认配置: {str(e)} - By @ConceptualGod")
            self.alpha_value = 0.85

    def save_config(self):
        """
        保存配置文件
        开发者: @ConceptualGod
        """
        try:
            config = {
                'alpha_value': self.alpha_value,
                'last_save_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"配置保存成功 - By @ConceptualGod")

        except Exception as e:
            print(f"保存配置失败: {str(e)} - By @ConceptualGod")

    def _set_window_icon(self):
        """
        设置窗口图标
        开发者: @ConceptualGod
        """
        try:
            if not PIL_AVAILABLE:
                print("PIL库未安装，无法设置PNG图标 - By @ConceptualGod")
                return

            # 获取logo文件路径
            # coordinate_recorder.py在QFL目录下，logo文件夹也在QFL目录下
            current_dir = os.path.dirname(__file__)  # QFL目录
            logo_path = os.path.join(current_dir, "logo", "logo.png")

            if os.path.exists(logo_path):
                # 加载PNG图标
                image = Image.open(logo_path)

                # 调整图标大小 (32x32 for window icon)
                icon_image = image.resize((32, 32), Image.Resampling.LANCZOS)
                self.icon_photo = ImageTk.PhotoImage(icon_image)

                # 设置窗口图标
                self.root.iconphoto(True, self.icon_photo)

                # 调整任务栏图标大小 (16x16 for taskbar)
                taskbar_image = image.resize((16, 16), Image.Resampling.LANCZOS)
                self.taskbar_photo = ImageTk.PhotoImage(taskbar_image)

                # 设置任务栏图标
                self.root.iconphoto(False, self.taskbar_photo)

                print(f"坐标录制器图标设置成功: {logo_path} - By @ConceptualGod")
            else:
                print(f"图标文件不存在: {logo_path} - By @ConceptualGod")

        except Exception as e:
            print(f"设置窗口图标失败: {str(e)} - By @ConceptualGod")


def main():
    """
    主函数
    开发者: @ConceptualGod
    """
    try:
        print("启动坐标录制器 - By @ConceptualGod")

        # 创建并运行坐标录制器
        recorder = CoordinateRecorder()
        recorder.run()

    except Exception as e:
        print(f"启动坐标录制器失败: {str(e)} - By @ConceptualGod")


if __name__ == "__main__":
    main()
