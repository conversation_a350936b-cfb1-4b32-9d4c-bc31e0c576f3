#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡游戏自动化GUI程序主入口

开发者: @ConceptualGod
"""

import sys
import os
import subprocess
from pathlib import Path

def run_install():
    """运行install.py进行环境安装"""
    print("起凡自动化脚本 By @ConceptualGod")
    print("=" * 40)
    print("正在检查和安装环境...")

    try:
        # 运行install.py
        current_dir = Path(__file__).parent
        result = subprocess.run([sys.executable, "install.py"],
                              cwd=current_dir)

        if result.returncode == 0:
            print("\n环境检查完成，正在启动GUI... - By @ConceptualGod")
            return True
        else:
            print("\n环境安装失败 - By @ConceptualGod")
            return False

    except Exception as e:
        print(f"运行install.py时出现异常: {e}")
        return False

def main():
    """主函数"""
    # 添加当前目录到Python路径
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))

    # 设置工作目录
    os.chdir(current_dir)

    # 先运行install.py确保环境已安装
    if not run_install():
        input("按回车键退出...")
        return

    try:
        # 启动GUI
        from gui.main_window import main as gui_main
        gui_main()

    except ImportError as e:
        print("=" * 50)
        print(f"导入GUI模块失败: {e} - By @ConceptualGod")
        print("环境可能未正确安装，请检查错误信息 - By @ConceptualGod")
        print("=" * 50)
        input("按回车键退出...")
    except Exception as e:
        print("=" * 50)
        print(f"程序启动失败: {e} - By @ConceptualGod")
        print("=" * 50)
        input("按回车键退出...")

if __name__ == "__main__":
    main()
