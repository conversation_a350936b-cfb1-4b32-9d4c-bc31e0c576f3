#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标操作管理器
专门处理可以用固定坐标实现的操作

开发者: @ConceptualGod
Date: 2025-07-24
"""

import json
import time
import pyautogui
import logging
from typing import Dict, List, Optional, Callable, Tuple
from pathlib import Path

class CoordinateOperationManager:
    def __init__(self, log_callback: Optional[Callable] = None):
        """初始化坐标操作管理器"""
        self.log_callback = log_callback
        self.setup_logging()
        
        # 坐标数据
        self.coordinates = {}
        self.load_coordinates()
        
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.3
        
        self._log("坐标操作管理器初始化完成 - By @ConceptualGod")
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('coordinate_operation_manager')
    
    def _log(self, message: str, level: str = "INFO"):
        """统一日志输出"""
        formatted_message = f"{message} - By @ConceptualGod"
        
        if self.log_callback:
            self.log_callback(formatted_message)
        
        if level == "ERROR":
            self.logger.error(formatted_message)
        elif level == "WARNING":
            self.logger.warning(formatted_message)
        else:
            self.logger.info(formatted_message)
    
    def load_coordinates(self):
        """加载坐标数据"""
        try:
            current_dir = Path(__file__).parent
            coords_file = current_dir / "recorded_coordinates_categorized.json"
            
            if coords_file.exists():
                with open(coords_file, 'r', encoding='utf-8') as f:
                    coords_data = json.load(f)
                
                # 整合所有坐标到一个字典中
                self.coordinates = {}
                
                # 英雄坐标
                if 'heroes' in coords_data:
                    for hero in coords_data['heroes']:
                        self.coordinates[hero['name']] = (hero['x'], hero['y'])
                
                # 操作按钮
                if 'action_buttons' in coords_data:
                    for button in coords_data['action_buttons']:
                        self.coordinates[button['name']] = (button['x'], button['y'])
                
                # 界面元素
                if 'interface_elements' in coords_data:
                    for element in coords_data['interface_elements']:
                        self.coordinates[element['name']] = (element['x'], element['y'])
                
                # 装备道具
                if 'equipment_items' in coords_data:
                    for item in coords_data['equipment_items']:
                        self.coordinates[item['name']] = (item['x'], item['y'])
                
                # 属性显示
                if 'attribute_displays' in coords_data:
                    for attr in coords_data['attribute_displays']:
                        self.coordinates[attr['name']] = (attr['x'], attr['y'])
                
                self._log(f"成功加载 {len(self.coordinates)} 个坐标 - By @ConceptualGod")
            else:
                self._log("坐标文件不存在，使用空坐标集", "WARNING")
                
        except Exception as e:
            self._log(f"坐标加载失败: {str(e)} - By @ConceptualGod", "ERROR")
            self.coordinates = {}
    
    def click_coordinate(self, coord_name: str, wait_time: float = 1.0) -> bool:
        """点击指定坐标"""
        try:
            if coord_name not in self.coordinates:
                self._log(f"未找到坐标: {coord_name}", "ERROR")
                return False
            
            x, y = self.coordinates[coord_name]
            self._log(f"点击坐标: {coord_name} ({x}, {y})")
            
            pyautogui.click(x, y)
            time.sleep(wait_time)
            
            return True
            
        except Exception as e:
            self._log(f"点击坐标失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def double_click_coordinate(self, coord_name: str, wait_time: float = 1.0) -> bool:
        """双击指定坐标"""
        try:
            if coord_name not in self.coordinates:
                self._log(f"未找到坐标: {coord_name}", "ERROR")
                return False
            
            x, y = self.coordinates[coord_name]
            self._log(f"双击坐标: {coord_name} ({x}, {y})")
            
            pyautogui.doubleClick(x, y)
            time.sleep(wait_time)
            
            return True
            
        except Exception as e:
            self._log(f"双击坐标失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def drag_coordinate(self, from_coord: str, to_coord: str, duration: float = 1.0) -> bool:
        """拖拽操作"""
        try:
            if from_coord not in self.coordinates or to_coord not in self.coordinates:
                self._log(f"拖拽坐标不存在: {from_coord} -> {to_coord}", "ERROR")
                return False
            
            from_x, from_y = self.coordinates[from_coord]
            to_x, to_y = self.coordinates[to_coord]
            
            self._log(f"拖拽操作: {from_coord} ({from_x}, {from_y}) -> {to_coord} ({to_x}, {to_y})")
            
            pyautogui.drag(from_x, from_y, to_x - from_x, to_y - from_y, duration=duration)
            time.sleep(1.0)
            
            return True
            
        except Exception as e:
            self._log(f"拖拽操作失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    # 装备相关操作
    def use_equipment_slot(self, slot_number: int) -> bool:
        """使用装备槽位"""
        try:
            # 装备槽位的快捷键通常是数字键1-9
            if 1 <= slot_number <= 9:
                self._log(f"使用装备槽位 {slot_number}")
                pyautogui.press(str(slot_number))
                time.sleep(0.5)
                return True
            else:
                self._log(f"无效的装备槽位: {slot_number}", "ERROR")
                return False
                
        except Exception as e:
            self._log(f"使用装备槽位失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def use_xuantie_shield(self) -> bool:
        """使用玄铁盾（固定在第2槽位）"""
        self._log("使用玄铁盾")
        return self.use_equipment_slot(2)
    
    def use_benlei_boots(self) -> bool:
        """使用奔雷靴（固定在第1槽位）"""
        self._log("使用奔雷靴")
        return self.use_equipment_slot(1)
    
    # 技能相关操作
    def cast_skill(self, skill_key: str) -> bool:
        """释放技能"""
        try:
            self._log(f"释放技能: {skill_key}")
            pyautogui.press(skill_key.lower())
            time.sleep(0.5)
            return True
            
        except Exception as e:
            self._log(f"释放技能失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def cast_heal_skills(self, hero_name: str) -> bool:
        """释放治疗技能"""
        # 英雄治疗技能配置
        heal_skills = {
            '华佗': ['W', 'D'],
            '刘备': ['C', 'E'],
            '诸葛瑾': ['E', 'W'],
            '陆逊': ['E'],
            '孙权': ['E'],
            '曹操': ['C']
        }
        
        if hero_name not in heal_skills:
            self._log(f"未知英雄: {hero_name}", "ERROR")
            return False
        
        self._log(f"释放 {hero_name} 的治疗技能")
        
        for skill in heal_skills[hero_name]:
            if self.cast_skill(skill):
                self._log(f"释放治疗技能: {skill}")
            else:
                self._log(f"释放治疗技能失败: {skill} - By @ConceptualGod", "WARNING")
        
        return True
    
    def cast_attack_skills(self, hero_name: str) -> bool:
        """释放攻击技能"""
        # 英雄攻击技能配置
        attack_skills = {
            '华佗': ['Q', 'E'],
            '刘备': ['Q', 'W'],
            '诸葛瑾': ['Q', 'C'],
            '陆逊': ['Q', 'W', 'C'],
            '孙权': ['Q', 'W', 'C'],
            '曹操': ['Q', 'W', 'E']
        }
        
        if hero_name not in attack_skills:
            self._log(f"未知英雄: {hero_name}", "ERROR")
            return False
        
        self._log(f"释放 {hero_name} 的攻击技能")
        
        for skill in attack_skills[hero_name]:
            if self.cast_skill(skill):
                self._log(f"释放攻击技能: {skill}")
            else:
                self._log(f"释放攻击技能失败: {skill} - By @ConceptualGod", "WARNING")
        
        return True
    
    # 购买相关操作
    def open_shop(self) -> bool:
        """打开商店"""
        self._log("按B键打开商店")
        try:
            pyautogui.press('b')
            time.sleep(2)  # 等待商店界面打开
            return True
        except Exception as e:
            self._log(f"打开商店失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def buy_speed_boots(self) -> bool:
        """购买速度之靴"""
        if "速度之靴" in self.coordinates:
            return self.click_coordinate("速度之靴", 2.0)
        else:
            self._log("未找到速度之靴坐标", "ERROR")
            return False
    
    def upgrade_health_attribute(self) -> bool:
        """升级生命值属性"""
        try:
            # 点击加号
            if not self.click_coordinate("点击加号", 1.0):
                return False
            
            # 点击生命值
            if not self.click_coordinate("生命值", 1.0):
                return False
            
            # 点击确定
            if not self.click_coordinate("确定", 2.0):
                return False
            
            self._log("生命值属性升级完成 - By @ConceptualGod")
            return True
            
        except Exception as e:
            self._log(f"升级生命值属性失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    # 其他操作
    def return_to_base(self) -> bool:
        """回城"""
        self._log("按Y键回城")
        try:
            pyautogui.press('y')
            time.sleep(3)  # 等待回城完成
            return True
        except Exception as e:
            self._log(f"回城失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def reroll_lootbox(self) -> bool:
        """重转锦囊"""
        self._log("按空格键重转锦囊")
        try:
            pyautogui.press('space')
            time.sleep(1)  # 等待重转完成
            return True
        except Exception as e:
            self._log(f"重转锦囊失败: {str(e)} - By @ConceptualGod", "ERROR")
            return False
    
    def get_available_coordinates(self) -> List[str]:
        """获取可用的坐标名称列表"""
        return list(self.coordinates.keys())
    
    def add_coordinate(self, name: str, x: int, y: int):
        """添加新坐标"""
        self.coordinates[name] = (x, y)
        self._log(f"添加坐标: {name} ({x}, {y})")
    
    def get_coordinate(self, name: str) -> Optional[Tuple[int, int]]:
        """获取指定坐标"""
        return self.coordinates.get(name)
