#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标验证工具
用于验证和调整任务区域坐标

开发者: @ConceptualGod
"""

import pyautogui
import json
import os
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
import cv2
import numpy as np

class CoordinateValidator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("坐标验证工具")
        self.root.geometry("800x600")
        
        self.current_coords = {"x": 408, "y": 612, "width": 564, "height": 272}
        self.screenshot = None
        self.preview_image = None
        
        self.setup_ui()
        self.load_current_coords()
        
    def setup_ui(self):
        """设置用户界面"""
        # 坐标输入框
        coords_frame = ttk.Frame(self.root)
        coords_frame.pack(pady=10, padx=10, fill="x")
        
        ttk.Label(coords_frame, text="X:").grid(row=0, column=0, padx=5)
        self.x_var = tk.StringVar(value=str(self.current_coords["x"]))
        ttk.Entry(coords_frame, textvariable=self.x_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(coords_frame, text="Y:").grid(row=0, column=2, padx=5)
        self.y_var = tk.StringVar(value=str(self.current_coords["y"]))
        ttk.Entry(coords_frame, textvariable=self.y_var, width=10).grid(row=0, column=3, padx=5)
        
        ttk.Label(coords_frame, text="宽度:").grid(row=0, column=4, padx=5)
        self.width_var = tk.StringVar(value=str(self.current_coords["width"]))
        ttk.Entry(coords_frame, textvariable=self.width_var, width=10).grid(row=0, column=5, padx=5)
        
        ttk.Label(coords_frame, text="高度:").grid(row=0, column=6, padx=5)
        self.height_var = tk.StringVar(value=str(self.current_coords["height"]))
        ttk.Entry(coords_frame, textvariable=self.height_var, width=10).grid(row=0, column=7, padx=5)
        
        # 按钮
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="截取预览", command=self.capture_preview).pack(side="left", padx=5)
        ttk.Button(button_frame, text="保存坐标", command=self.save_coordinates).pack(side="left", padx=5)
        ttk.Button(button_frame, text="全屏截图", command=self.capture_fullscreen).pack(side="left", padx=5)
        
        # 预览区域
        self.preview_label = ttk.Label(self.root, text="预览区域")
        self.preview_label.pack(pady=10, expand=True, fill="both")
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken")
        status_bar.pack(side="bottom", fill="x")
        
    def load_current_coords(self):
        """加载当前坐标配置"""
        try:
            task_file = "task.json"
            if os.path.exists(task_file):
                with open(task_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if 'task_region' in config and 'coordinates' in config['task_region']:
                        coords = config['task_region']['coordinates']
                        self.current_coords = coords
                        self.x_var.set(str(coords["x"]))
                        self.y_var.set(str(coords["y"]))
                        self.width_var.set(str(coords["width"]))
                        self.height_var.set(str(coords["height"]))
                        self.status_var.set("已加载现有坐标配置")
        except Exception as e:
            self.status_var.set(f"加载坐标配置失败: {str(e)}")
            
    def get_current_coords(self):
        """获取当前输入的坐标"""
        try:
            return {
                "x": int(self.x_var.get()),
                "y": int(self.y_var.get()),
                "width": int(self.width_var.get()),
                "height": int(self.height_var.get())
            }
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字 - By @ConceptualGod")
            return None
            
    def capture_preview(self):
        """截取预览 - 已移除PIL依赖"""
        coords = self.get_current_coords()
        if not coords:
            return

        try:
            self.status_var.set("正在截取预览...")

            # 使用OpenCV截取指定区域
            import pyautogui
            import numpy as np

            # 截取指定区域并转换为numpy数组
            screenshot = pyautogui.screenshot(region=(coords["x"], coords["y"], coords["width"], coords["height"]))
            screenshot_np = np.array(screenshot)

            # 保存调试图片
            debug_filename = f"coordinate_preview_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            cv2.imwrite(debug_filename, cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR))

            # 显示预览信息
            self.show_preview(None)

            self.status_var.set(f"预览截取完成，已保存: {debug_filename}")

        except Exception as e:
            self.status_var.set(f"截取预览失败: {str(e)}")
            messagebox.showerror("错误", f"截取预览失败: {str(e)} - By @ConceptualGod")
            
    def capture_fullscreen(self):
        """截取全屏 - 已移除PIL依赖"""
        try:
            self.status_var.set("正在截取全屏...")

            # 使用OpenCV截取全屏
            import pyautogui
            import numpy as np

            # 截取全屏并转换为numpy数组
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)

            # 保存全屏图片
            fullscreen_filename = f"fullscreen_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            cv2.imwrite(fullscreen_filename, cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR))

            self.status_var.set(f"全屏截取完成，已保存: {fullscreen_filename}")
            messagebox.showinfo("完成", f"全屏截图已保存: {fullscreen_filename} - By @ConceptualGod")

        except Exception as e:
            self.status_var.set(f"截取全屏失败: {str(e)}")
            messagebox.showerror("错误", f"截取全屏失败: {str(e)} - By @ConceptualGod")
            
    def show_preview(self, image):
        """显示预览图片 - 已移除PIL依赖"""
        # 移除PIL依赖的预览功能
        self.preview_label.configure(image="", text="预览功能已移除 (无PIL依赖) - By @ConceptualGod")
            
    def save_coordinates(self):
        """保存坐标配置"""
        coords = self.get_current_coords()
        if not coords:
            return
            
        try:
            # 创建配置
            config = {
                "task_region": {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "coordinates": coords,
                    "scan_range": {
                        "left": coords["x"],
                        "top": coords["y"],
                        "right": coords["x"] + coords["width"],
                        "bottom": coords["y"] + coords["height"]
                    },
                    "screen_resolution": f"{pyautogui.size().width}x{pyautogui.size().height}",
                    "description": f"任务区域 - 左上角({coords['x']}, {coords['y']}) 到 右下角({coords['x'] + coords['width']}, {coords['y'] + coords['height']})"
                }
            }
            
            # 保存到文件
            with open("task.json", 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            self.status_var.set("坐标配置已保存到 task.json")
            messagebox.showinfo("完成", "坐标配置已保存 - By @ConceptualGod")
            
        except Exception as e:
            self.status_var.set(f"保存坐标配置失败: {str(e)}")
            messagebox.showerror("错误", f"保存坐标配置失败: {str(e)} - By @ConceptualGod")
            
    def run(self):
        """运行工具"""
        self.root.mainloop()

if __name__ == "__main__":
    validator = CoordinateValidator()
    validator.run()
