******** System Locale: Chinese (Simplified)_China.936
++++++[15412] Start log thread
[15412] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18532 -s 47.101.217.129:3080 -p 57718 -u 85960934
Wed Jun 18 2025 12:48:08.827000 [22420] LM_TRACE Start to parse argument
Wed Jun 18 2025 12:48:08.895000 [22420] LM_TRACE Parse parameter 18532 47.101.217.129:3080 57718 85960934
Wed Jun 18 2025 12:48:09.816000 [22420] LM_DEBUG Service port: 61607
++++++Wed Jun 18 2025 12:48:09.816000 (16996) AudioSocketEventTask started
++++++Wed Jun 18 2025 12:48:09.816000 (24092) ReactorEventTask started
Wed Jun 18 2025 12:48:09.816000 [22420] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Wed Jun 18 2025 12:48:10.466000 [22420] LM_TRACE Platform info 2130706433:57718
Wed Jun 18 2025 12:48:10.524000 [22420] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jun 18 2025 12:48:10.524000 [24092] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jun 18 2025 12:48:10.771000 [22420] LM_ERROR failed to call waveInOpen(1)
Wed Jun 18 2025 12:48:10.771000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18532 -s 47.101.217.129:3080 -p 57718 -u 85960934
++++++Wed Jun 18 2025 12:48:10.856000 (24016) ProactorEventTask started
Wed Jun 18 2025 12:48:10.856000 [22420] Creating endpoint instance...
Wed Jun 18 2025 12:48:10.923000 [22420] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jun 18 2025 12:48:10.923000 [22420] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jun 18 2025 12:48:10.924000 [23168] LM_TRACE Start Udp data dispatcher
++++++Wed Jun 18 2025 12:48:10.924000 [6720] LM_TRACE Startting auido data processor
Wed Jun 18 2025 12:48:10.938000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jun 18 2025 12:48:10.938000 [6828] Start command executor
Wed Jun 18 2025 12:48:10.938000 LM_TRACE [6828] cmd02D4CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 12:48:10.938000 [6828] LM_TRACE Set user info, sid: 87621406, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 12:48:10.939000 [6828] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jun 18 2025 12:48:10.939000 [6828] LM_TRACE CmdSetUserInfo update process info
Wed Jun 18 2025 12:48:10.939000 [6828] LM_TRACE CmdSetUserInfo execute successfully
Wed Jun 18 2025 12:48:10.939000 LM_TRACE [6828] cmd02D4C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 12:48:10.939000 [6828] LM_TRACE User id: 85960934, sid: 87621406, tid: 0
Wed Jun 18 2025 12:48:10.939000 [6828] LM_TRACE Set user info, sid: 87621406, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 12:48:20.776000 [24092] LM_TRACE Process ID 18532, uid: 85960934
Wed Jun 18 2025 12:48:20.776000 LM_TRACE [6828] cmd02D4D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jun 18 2025 12:48:30.763000 [23168] LM_TRACE Exit Udp data dispatcher
++++++Wed Jun 18 2025 12:48:30.763000 [6720] LM_TRACE Stopping auido data processor
++++++[15412] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[25572] Start log thread
[25572] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 22340 -s 47.101.217.129:3080 -p 52709 -u 85960934
Wed Jun 18 2025 12:51:58.503000 [9284] LM_TRACE Start to parse argument
Wed Jun 18 2025 12:51:58.503000 [9284] LM_TRACE Parse parameter 22340 47.101.217.129:3080 52709 85960934
Wed Jun 18 2025 12:51:58.525000 [9284] LM_DEBUG Service port: 57449
Wed Jun 18 2025 12:51:58.526000 [9284] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jun 18 2025 12:51:58.541000 (15672) AudioSocketEventTask started
++++++Wed Jun 18 2025 12:51:58.542000 (26416) ReactorEventTask started
Wed Jun 18 2025 12:51:58.667000 [9284] LM_TRACE Platform info 2130706433:52709
Wed Jun 18 2025 12:51:58.667000 [9284] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jun 18 2025 12:51:58.683000 [26416] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jun 18 2025 12:51:58.901000 [9284] LM_ERROR failed to call waveInOpen(1)
Wed Jun 18 2025 12:51:58.901000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 22340 -s 47.101.217.129:3080 -p 52709 -u 85960934
++++++Wed Jun 18 2025 12:51:59.946000 (15916) ProactorEventTask started
Wed Jun 18 2025 12:51:59.947000 [9284] Creating endpoint instance...
Wed Jun 18 2025 12:51:59.947000 [9284] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jun 18 2025 12:51:59.947000 [9284] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jun 18 2025 12:51:59.948000 [11552] LM_TRACE Start Udp data dispatcher
++++++Wed Jun 18 2025 12:51:59.949000 [13988] LM_TRACE Startting auido data processor
Wed Jun 18 2025 12:52:00.015000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jun 18 2025 12:52:00.015000 [24040] Start command executor
Wed Jun 18 2025 12:52:00.016000 LM_TRACE [24040] cmd0290CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 12:52:00.016000 [24040] LM_TRACE Set user info, sid: 88203390, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 12:52:00.016000 [24040] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jun 18 2025 12:52:00.016000 [24040] LM_TRACE CmdSetUserInfo update process info
Wed Jun 18 2025 12:52:00.016000 [24040] LM_TRACE CmdSetUserInfo execute successfully
Wed Jun 18 2025 12:52:00.016000 LM_TRACE [24040] cmd0290C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 12:52:00.016000 [24040] LM_TRACE User id: 85960934, sid: 88203390, tid: 0
Wed Jun 18 2025 12:52:00.016000 [24040] LM_TRACE Set user info, sid: 88203390, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 13:02:56.144000 [26416] LM_TRACE Process ID 22340, uid: 85960934
Wed Jun 18 2025 13:02:56.430000 LM_TRACE [24040] cmd0290D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jun 18 2025 13:02:59.098000 [11552] LM_TRACE Exit Udp data dispatcher
++++++[25572] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[17620] Start log thread
[17620] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17880 -s 47.101.217.129:3080 -p 52750 -u 85960934
Wed Jun 18 2025 13:12:32.361000 [25792] LM_TRACE Start to parse argument
Wed Jun 18 2025 13:12:32.416000 [25792] LM_TRACE Parse parameter 17880 47.101.217.129:3080 52750 85960934
Wed Jun 18 2025 13:12:32.791000 [25792] LM_DEBUG Service port: 49970
Wed Jun 18 2025 13:12:32.793000 [25792] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jun 18 2025 13:12:32.793000 (4232) AudioSocketEventTask started
++++++Wed Jun 18 2025 13:12:32.794000 (16464) ReactorEventTask started
Wed Jun 18 2025 13:12:34.723000 [25792] LM_TRACE Platform info 2130706433:52750
Wed Jun 18 2025 13:12:34.826000 [25792] LM_INFO Try to initialize volume mixer control(-1, -1)
Wed Jun 18 2025 13:12:34.941000 [25792] LM_ERROR failed to call waveInOpen(1)
Wed Jun 18 2025 13:12:34.941000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17880 -s 47.101.217.129:3080 -p 52750 -u 85960934
++++++Wed Jun 18 2025 13:12:35.013000 (7664) ProactorEventTask started
Wed Jun 18 2025 13:12:35.065000 [25792] Creating endpoint instance...
Wed Jun 18 2025 13:12:35.112000 [25792] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jun 18 2025 13:12:35.177000 [25792] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jun 18 2025 13:12:35.177000 [13280] LM_TRACE Start Udp data dispatcher
++++++Wed Jun 18 2025 13:12:35.265000 [2940] LM_TRACE Startting auido data processor
Wed Jun 18 2025 13:12:35.273000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jun 18 2025 13:12:35.273000 [12884] Start command executor
'Wed Jun 18 2025 13:12:35.515000 [16464] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jun 18 2025 13:12:35.515000 LM_TRACE [12884] cmd02B4CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 13:12:35.515000 [12884] LM_TRACE Set user info, sid: 89087515, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 13:12:35.515000 [12884] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jun 18 2025 13:12:35.515000 [12884] LM_TRACE CmdSetUserInfo update process info
Wed Jun 18 2025 13:12:35.516000 [12884] LM_TRACE CmdSetUserInfo execute successfully
Wed Jun 18 2025 13:12:35.516000 LM_TRACE [12884] cmd02B4C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 13:12:35.516000 [12884] LM_TRACE User id: 85960934, sid: 89087515, tid: 0
Wed Jun 18 2025 13:12:35.516000 [12884] LM_TRACE Set user info, sid: 89087515, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 13:12:43.711000 [16464] LM_TRACE Process ID 17880, uid: 85960934
Wed Jun 18 2025 13:12:43.711000 LM_TRACE [12884] cmd02B4D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jun 18 2025 13:12:44.935000 [13280] LM_TRACE Exit Udp data dispatcher
++++++Wed Jun 18 2025 13:12:44.935000 [2940] LM_TRACE Stopping auido data processor
++++++[17620] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jun 18 2025 13:14:29.744000 [23024] LM_TRACE Start to parse argument
Wed Jun 18 2025 13:14:29.744000 [23024] LM_TRACE Parse parameter 10168 47.101.217.129:3080 58935 85960934
++++++[11420] Start log thread
[11420] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 10168 -s 47.101.217.129:3080 -p 58935 -u 85960934
Wed Jun 18 2025 13:14:29.803000 [23024] LM_DEBUG Service port: 59061
Wed Jun 18 2025 13:14:29.804000 [23024] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jun 18 2025 13:14:29.819000 (12336) AudioSocketEventTask started
++++++Wed Jun 18 2025 13:14:29.819000 (18460) ReactorEventTask started
Wed Jun 18 2025 13:14:30.321000 [23024] LM_TRACE Platform info 2130706433:58935
Wed Jun 18 2025 13:14:30.322000 [23024] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jun 18 2025 13:14:30.323000 [18460] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jun 18 2025 13:14:30.776000 [23024] LM_ERROR failed to call waveInOpen(1)
Wed Jun 18 2025 13:14:30.777000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 10168 -s 47.101.217.129:3080 -p 58935 -u 85960934
++++++Wed Jun 18 2025 13:14:31.223000 (12632) ProactorEventTask started
Wed Jun 18 2025 13:14:31.224000 [23024] Creating endpoint instance...
Wed Jun 18 2025 13:14:31.224000 [23024] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jun 18 2025 13:14:31.224000 [23024] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jun 18 2025 13:14:31.226000 [24336] LM_TRACE Start Udp data dispatcher
++++++Wed Jun 18 2025 13:14:31.226000 [23448] LM_TRACE Startting auido data processor
Wed Jun 18 2025 13:14:31.247000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jun 18 2025 13:14:31.248000 [10488] Start command executor
Wed Jun 18 2025 13:14:31.248000 LM_TRACE [10488] cmd02A1CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 13:14:31.248000 [10488] LM_TRACE Set user info, sid: 89670093, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 13:14:31.248000 [10488] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jun 18 2025 13:14:31.248000 [10488] LM_TRACE CmdSetUserInfo update process info
Wed Jun 18 2025 13:14:31.248000 [10488] LM_TRACE CmdSetUserInfo execute successfully
Wed Jun 18 2025 13:14:31.248000 LM_TRACE [10488] cmd02A1C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 13:14:31.249000 [10488] LM_TRACE User id: 85960934, sid: 89670093, tid: 0
Wed Jun 18 2025 13:14:31.249000 [10488] LM_TRACE Set user info, sid: 89670093, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 13:18:41.557000 [18460] LM_TRACE Process ID 10168, uid: 85960934
Wed Jun 18 2025 13:18:41.558000 LM_TRACE [10488] cmd02A1D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jun 18 2025 13:18:50.826000 [24336] LM_TRACE Exit Udp data dispatcher
++++++[11420] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[20088] Start log thread
[20088] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15992 -s 47.101.217.129:3080 -p 56708 -u 85960934
Wed Jun 18 2025 13:19:59.246000 [16284] LM_TRACE Start to parse argument
Wed Jun 18 2025 13:19:59.246000 [16284] LM_TRACE Parse parameter 15992 47.101.217.129:3080 56708 85960934
Wed Jun 18 2025 13:19:59.265000 [16284] LM_DEBUG Service port: 51387
Wed Jun 18 2025 13:19:59.266000 [16284] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jun 18 2025 13:19:59.266000 (16148) AudioSocketEventTask started
++++++Wed Jun 18 2025 13:19:59.266000 (24460) ReactorEventTask started
Wed Jun 18 2025 13:19:59.785000 [16284] LM_TRACE Platform info 2130706433:56708
Wed Jun 18 2025 13:19:59.785000 [16284] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jun 18 2025 13:19:59.791000 [24460] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jun 18 2025 13:20:00.549000 [16284] LM_ERROR failed to call waveInOpen(1)
Wed Jun 18 2025 13:20:00.549000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15992 -s 47.101.217.129:3080 -p 56708 -u 85960934
++++++Wed Jun 18 2025 13:20:00.955000 (25328) ProactorEventTask started
Wed Jun 18 2025 13:20:00.955000 [16284] Creating endpoint instance...
Wed Jun 18 2025 13:20:00.955000 [16284] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jun 18 2025 13:20:00.955000 [16284] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jun 18 2025 13:20:00.956000 [8500] LM_TRACE Start Udp data dispatcher
++++++Wed Jun 18 2025 13:20:00.957000 [20884] LM_TRACE Startting auido data processor
Wed Jun 18 2025 13:20:01.002000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jun 18 2025 13:20:01.002000 [25540] Start command executor
Wed Jun 18 2025 13:20:01.002000 LM_TRACE [25540] cmd02ADCE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 13:20:01.002000 [25540] LM_TRACE Set user info, sid: 90023921, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 13:20:01.002000 [25540] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jun 18 2025 13:20:01.002000 [25540] LM_TRACE CmdSetUserInfo update process info
Wed Jun 18 2025 13:20:01.003000 [25540] LM_TRACE CmdSetUserInfo execute successfully
Wed Jun 18 2025 13:20:01.003000 LM_TRACE [25540] cmd02ADC9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jun 18 2025 13:20:01.003000 [25540] LM_TRACE User id: 85960934, sid: 90023921, tid: 0
Wed Jun 18 2025 13:20:01.003000 [25540] LM_TRACE Set user info, sid: 90023921, uid 85960934, tid: 0, game room id:0
Wed Jun 18 2025 15:10:25.436000 [24460] LM_TRACE Process ID 15992, uid: 85960934
Wed Jun 18 2025 15:10:25.817000 LM_TRACE [25540] cmd02ADD2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jun 18 2025 15:10:31.008000 [8500] LM_TRACE Exit Udp data dispatcher
++++++[20088] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[14680] Start log thread
[14680] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 8464 -s 47.101.217.129:3080 -p 57296 -u 85960934
Fri Jun 20 2025 01:14:29.518000 [6952] LM_TRACE Start to parse argument
Fri Jun 20 2025 01:14:29.519000 [6952] LM_TRACE Parse parameter 8464 47.101.217.129:3080 57296 85960934
Fri Jun 20 2025 01:14:29.568000 [6952] LM_DEBUG Service port: 61438
Fri Jun 20 2025 01:14:29.569000 [6952] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 01:14:29.575000 (15760) AudioSocketEventTask started
++++++Fri Jun 20 2025 01:14:29.575000 (14400) ReactorEventTask started
Fri Jun 20 2025 01:14:30.358000 [6952] LM_TRACE Platform info 2130706433:57296
Fri Jun 20 2025 01:14:30.358000 [6952] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 01:14:31.089000 [14400] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 01:14:31.288000 [6952] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 01:14:31.288000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 8464 -s 47.101.217.129:3080 -p 57296 -u 85960934
++++++Fri Jun 20 2025 01:14:33.402000 (20668) ProactorEventTask started
Fri Jun 20 2025 01:14:33.402000 [6952] Creating endpoint instance...
Fri Jun 20 2025 01:14:33.402000 [6952] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 01:14:33.402000 [6952] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 01:14:33.457000 [18284] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 01:14:33.457000 [20572] LM_TRACE Startting auido data processor
Fri Jun 20 2025 01:14:33.777000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 01:14:33.925000 [4588] Start command executor
Fri Jun 20 2025 01:14:33.925000 LM_TRACE [4588] cmd0101CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 01:14:33.925000 [4588] LM_TRACE Set user info, sid: 1122015, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 01:14:33.925000 [4588] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 01:14:33.925000 [4588] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 01:14:33.925000 [4588] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 01:14:33.925000 LM_TRACE [4588] cmd0101C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 01:14:33.925000 [4588] LM_TRACE User id: 85960934, sid: 1122015, tid: 0
Fri Jun 20 2025 01:14:33.925000 [4588] LM_TRACE Set user info, sid: 1122015, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 01:14:39.340000 [14400] LM_TRACE Process ID 8464, uid: 85960934
Fri Jun 20 2025 01:14:39.340000 LM_TRACE [4588] cmd0101D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 01:14:41.281000 [18284] LM_TRACE Exit Udp data dispatcher
++++++[14680] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Fri Jun 20 2025 01:40:53.935000 [15552] LM_TRACE Start to parse argument
Fri Jun 20 2025 01:40:53.958000 [15552] LM_TRACE Parse parameter 21316 47.101.217.129:3080 56115 85960934
++++++[17244] Start log thread
[17244] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 21316 -s 47.101.217.129:3080 -p 56115 -u 85960934
Fri Jun 20 2025 01:40:54.369000 [15552] LM_DEBUG Service port: 49184
Fri Jun 20 2025 01:40:54.385000 [15552] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 01:40:55.113000 (9156) AudioSocketEventTask started
++++++Fri Jun 20 2025 01:40:55.113000 (21448) ReactorEventTask started
Fri Jun 20 2025 01:40:57.514000 [15552] LM_TRACE Platform info 2130706433:56115
'Fri Jun 20 2025 01:40:57.557000 [21448] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 01:40:57.567000 [15552] LM_INFO Try to initialize volume mixer control(-1, -1)
Fri Jun 20 2025 01:41:04.230000 [15552] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 01:41:04.230000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 21316 -s 47.101.217.129:3080 -p 56115 -u 85960934
++++++Fri Jun 20 2025 01:41:04.712000 (16800) ProactorEventTask started
Fri Jun 20 2025 01:41:04.727000 [15552] Creating endpoint instance...
Fri Jun 20 2025 01:41:04.741000 [15552] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 01:41:04.741000 [15552] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 01:41:04.851000 [8612] LM_TRACE Startting auido data processor
++++++Fri Jun 20 2025 01:41:04.851000 [9964] LM_TRACE Start Udp data dispatcher
Fri Jun 20 2025 01:41:05.149000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 01:41:05.177000 [16556] Start command executor
Fri Jun 20 2025 01:41:05.177000 LM_TRACE [16556] cmd02FECE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 01:41:05.178000 [16556] LM_TRACE Set user info, sid: 3806921, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 01:41:05.178000 [16556] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 01:41:05.178000 [16556] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 01:41:05.178000 [16556] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 01:41:05.178000 LM_TRACE [16556] cmd02FEC9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 01:41:05.178000 [16556] LM_TRACE User id: 85960934, sid: 3806921, tid: 0
Fri Jun 20 2025 01:41:05.178000 [16556] LM_TRACE Set user info, sid: 3806921, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 01:41:20.836000 [21448] LM_TRACE Process ID 21316, uid: 85960934
Fri Jun 20 2025 01:41:20.836000 LM_TRACE [16556] cmd02FED2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 01:41:24.236000 [8612] LM_TRACE Stopping auido data processor
++++++Fri Jun 20 2025 01:41:24.236000 [9964] LM_TRACE Exit Udp data dispatcher
++++++[17244] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[12192] Start log thread
[12192] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20608 -s 47.101.217.129:3080 -p 56708 -u 85960934
Fri Jun 20 2025 01:48:59.830000 [13980] LM_TRACE Start to parse argument
Fri Jun 20 2025 01:48:59.831000 [13980] LM_TRACE Parse parameter 20608 47.101.217.129:3080 56708 85960934
Fri Jun 20 2025 01:49:00.122000 [13980] LM_DEBUG Service port: 54556
Fri Jun 20 2025 01:49:00.136000 [13980] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 01:49:00.252000 (12436) AudioSocketEventTask started
++++++Fri Jun 20 2025 01:49:00.255000 (21212) ReactorEventTask started
Fri Jun 20 2025 01:49:00.968000 [13980] LM_TRACE Platform info 2130706433:56708
Fri Jun 20 2025 01:49:00.968000 [13980] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 01:49:00.969000 [21212] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 01:49:02.880000 [13980] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 01:49:02.880000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20608 -s 47.101.217.129:3080 -p 56708 -u 85960934
++++++Fri Jun 20 2025 01:49:03.399000 (20416) ProactorEventTask started
Fri Jun 20 2025 01:49:03.399000 [13980] Creating endpoint instance...
Fri Jun 20 2025 01:49:03.399000 [13980] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 01:49:03.399000 [13980] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 01:49:03.400000 [3696] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 01:49:03.400000 [10044] LM_TRACE Startting auido data processor
Fri Jun 20 2025 01:49:03.446000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 01:49:03.447000 [18628] Start command executor
Fri Jun 20 2025 01:49:03.447000 LM_TRACE [18628] cmd0297CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 01:49:03.447000 [18628] LM_TRACE Set user info, sid: 5595828, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 01:49:03.447000 [18628] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 01:49:03.447000 [18628] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 01:49:03.447000 [18628] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 01:49:03.447000 LM_TRACE [18628] cmd0297C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 01:49:03.447000 [18628] LM_TRACE User id: 85960934, sid: 5595828, tid: 0
Fri Jun 20 2025 01:49:03.447000 [18628] LM_TRACE Set user info, sid: 5595828, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 01:50:00.907000 [21212] LM_TRACE Process ID 20608, uid: 85960934
Fri Jun 20 2025 01:50:00.908000 LM_TRACE [18628] cmd0297D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 01:50:02.948000 [3696] LM_TRACE Exit Udp data dispatcher
++++++[12192] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Fri Jun 20 2025 08:40:15.886000 [16696] LM_TRACE Start to parse argument
Fri Jun 20 2025 08:40:15.902000 [16696] LM_TRACE Parse parameter 20044 47.101.217.129:3080 50254 85960934
++++++[24412] Start log thread
[24412] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20044 -s 47.101.217.129:3080 -p 50254 -u 85960934
Fri Jun 20 2025 08:40:16.103000 [16696] LM_DEBUG Service port: 63499
Fri Jun 20 2025 08:40:16.104000 [16696] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 08:40:16.191000 (21968) AudioSocketEventTask started
++++++Fri Jun 20 2025 08:40:16.191000 (10568) ReactorEventTask started
Fri Jun 20 2025 08:40:23.379000 [16696] LM_TRACE Platform info 2130706433:50254
Fri Jun 20 2025 08:40:23.403000 [16696] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 08:40:23.403000 [10568] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 08:40:27.133000 [16696] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 08:40:27.133000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20044 -s 47.101.217.129:3080 -p 50254 -u 85960934
++++++Fri Jun 20 2025 08:40:28.600000 (20536) ProactorEventTask started
Fri Jun 20 2025 08:40:28.656000 [16696] Creating endpoint instance...
Fri Jun 20 2025 08:40:28.697000 [16696] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 08:40:28.697000 [16696] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 08:40:28.716000 [20456] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 08:40:28.833000 [14860] LM_TRACE Startting auido data processor
Fri Jun 20 2025 08:40:29.088000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 08:40:29.185000 [22184] Start command executor
Fri Jun 20 2025 08:40:29.185000 LM_TRACE [22184] cmd031CCE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 08:40:29.185000 [22184] LM_TRACE Set user info, sid: 5926890, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 08:40:29.185000 [22184] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 08:40:29.185000 [22184] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 08:40:29.185000 [22184] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 08:40:29.185000 LM_TRACE [22184] cmd031CC9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 08:40:29.185000 [22184] LM_TRACE User id: 85960934, sid: 5926890, tid: 0
Fri Jun 20 2025 08:40:29.185000 [22184] LM_TRACE Set user info, sid: 5926890, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 08:41:06.105000 [10568] LM_TRACE Process ID 20044, uid: 85960934
Fri Jun 20 2025 08:41:06.105000 LM_TRACE [22184] cmd031CD2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 08:41:07.123000 [20456] LM_TRACE Exit Udp data dispatcher
++++++Fri Jun 20 2025 08:41:07.123000 [14860] LM_TRACE Stopping auido data processor
++++++[24412] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Fri Jun 20 2025 09:28:44.142000 [18440] LM_TRACE Start to parse argument
Fri Jun 20 2025 09:28:44.142000 [18440] LM_TRACE Parse parameter 3696 47.101.217.129:3080 49665 85960934
++++++[18560] Start log thread
[18560] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 3696 -s 47.101.217.129:3080 -p 49665 -u 85960934
Fri Jun 20 2025 09:28:44.624000 [18440] LM_DEBUG Service port: 50541
Fri Jun 20 2025 09:28:44.626000 [18440] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 09:28:44.818000 (5280) AudioSocketEventTask started
++++++Fri Jun 20 2025 09:28:44.819000 (23524) ReactorEventTask started
Fri Jun 20 2025 09:28:47.699000 [18440] LM_TRACE Platform info 2130706433:49665
Fri Jun 20 2025 09:28:47.714000 [18440] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 09:28:49.440000 [23524] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 09:28:59.262000 [18440] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 09:28:59.262000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 3696 -s 47.101.217.129:3080 -p 49665 -u 85960934
++++++Fri Jun 20 2025 09:29:03.244000 (11416) ProactorEventTask started
Fri Jun 20 2025 09:29:03.350000 [18440] Creating endpoint instance...
Fri Jun 20 2025 09:29:03.428000 [18440] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 09:29:03.428000 [18440] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 09:29:03.724000 [23280] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 09:29:03.745000 [23792] LM_TRACE Startting auido data processor
Fri Jun 20 2025 09:29:04.148000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 09:29:04.430000 [24084] Start command executor
Fri Jun 20 2025 09:29:04.451000 LM_TRACE [24084] cmd00D9CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 09:29:04.452000 [24084] LM_TRACE Set user info, sid: 30620734, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 09:29:04.452000 [24084] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 09:29:04.452000 [24084] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 09:29:04.452000 [24084] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 09:29:04.452000 LM_TRACE [24084] cmd00D9C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 09:29:04.452000 [24084] LM_TRACE User id: 85960934, sid: 30620734, tid: 0
Fri Jun 20 2025 09:29:04.452000 [24084] LM_TRACE Set user info, sid: 30620734, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 09:30:21.950000 [23524] LM_TRACE Process ID 3696, uid: 85960934
Fri Jun 20 2025 09:30:21.978000 LM_TRACE [24084] cmd00D9D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 09:30:29.276000 [23792] LM_TRACE Stopping auido data processor
++++++Fri Jun 20 2025 09:30:29.276000 [23280] LM_TRACE Exit Udp data dispatcher
++++++[18560] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[13464] Start log thread
[13464] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18976 -s 47.101.217.129:3080 -p 55023 -u 85960934
Fri Jun 20 2025 10:03:28.879000 [1524] LM_TRACE Start to parse argument
Fri Jun 20 2025 10:03:28.940000 [1524] LM_TRACE Parse parameter 18976 47.101.217.129:3080 55023 85960934
Fri Jun 20 2025 10:03:29.465000 [1524] LM_DEBUG Service port: 58958
Fri Jun 20 2025 10:03:29.466000 [1524] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 10:03:29.517000 (18556) ReactorEventTask started
++++++Fri Jun 20 2025 10:03:29.517000 (24408) AudioSocketEventTask started
Fri Jun 20 2025 10:03:30.081000 [1524] LM_TRACE Platform info 2130706433:55023
Fri Jun 20 2025 10:03:30.081000 [1524] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 10:03:30.082000 [18556] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 10:03:30.605000 [1524] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 10:03:30.606000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18976 -s 47.101.217.129:3080 -p 55023 -u 85960934
++++++Fri Jun 20 2025 10:03:30.895000 (12532) ProactorEventTask started
Fri Jun 20 2025 10:03:30.895000 [1524] Creating endpoint instance...
Fri Jun 20 2025 10:03:30.961000 [1524] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 10:03:30.961000 [1524] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 10:03:30.977000 [13524] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 10:03:30.978000 [22376] LM_TRACE Startting auido data processor
Fri Jun 20 2025 10:03:31.123000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 10:03:31.140000 [17828] Start command executor
Fri Jun 20 2025 10:03:31.140000 LM_TRACE [17828] cmd02E3CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:03:31.140000 [17828] LM_TRACE Set user info, sid: 33560250, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:03:31.140000 [17828] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 10:03:31.141000 [17828] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 10:03:31.141000 [17828] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 10:03:31.141000 LM_TRACE [17828] cmd02E3C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:03:31.141000 [17828] LM_TRACE User id: 85960934, sid: 33560250, tid: 0
Fri Jun 20 2025 10:03:31.141000 [17828] LM_TRACE Set user info, sid: 33560250, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:03:34.941000 [18556] LM_TRACE Process ID 18976, uid: 85960934
Fri Jun 20 2025 10:03:34.941000 LM_TRACE [17828] cmd02E3D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 10:03:40.609000 [13524] LM_TRACE Exit Udp data dispatcher
++++++[13464] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Fri Jun 20 2025 10:04:43.921000 [20980] LM_TRACE Start to parse argument
Fri Jun 20 2025 10:04:43.921000 [20980] LM_TRACE Parse parameter 22304 47.101.217.129:3080 55299 85960934
++++++[24332] Start log thread
[24332] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 22304 -s 47.101.217.129:3080 -p 55299 -u 85960934
Fri Jun 20 2025 10:04:43.978000 [20980] LM_DEBUG Service port: 51428
Fri Jun 20 2025 10:04:43.979000 [20980] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 10:04:43.993000 (18920) AudioSocketEventTask started
++++++Fri Jun 20 2025 10:04:44.004000 (11368) ReactorEventTask started
Fri Jun 20 2025 10:04:44.908000 [20980] LM_TRACE Platform info 2130706433:55299
'Fri Jun 20 2025 10:04:44.909000 [11368] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 10:04:44.910000 [20980] LM_INFO Try to initialize volume mixer control(-1, -1)
Fri Jun 20 2025 10:04:45.410000 [20980] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 10:04:45.411000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 22304 -s 47.101.217.129:3080 -p 55299 -u 85960934
++++++Fri Jun 20 2025 10:04:45.547000 (24148) ProactorEventTask started
Fri Jun 20 2025 10:04:45.548000 [20980] Creating endpoint instance...
Fri Jun 20 2025 10:04:45.548000 [20980] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 10:04:45.548000 [20980] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 10:04:45.550000 [7456] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 10:04:45.551000 [20420] LM_TRACE Startting auido data processor
Fri Jun 20 2025 10:04:45.564000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 10:04:45.566000 [17324] Start command executor
Fri Jun 20 2025 10:04:45.566000 LM_TRACE [17324] cmd0265CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:04:45.567000 [17324] LM_TRACE Set user info, sid: 35544968, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:04:45.567000 [17324] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 10:04:45.567000 [17324] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 10:04:45.567000 [17324] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 10:04:45.567000 LM_TRACE [17324] cmd0265C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:04:45.567000 [17324] LM_TRACE User id: 85960934, sid: 35544968, tid: 0
Fri Jun 20 2025 10:04:45.567000 [17324] LM_TRACE Set user info, sid: 35544968, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:05:06.891000 [11368] LM_TRACE Process ID 22304, uid: 85960934
Fri Jun 20 2025 10:05:06.900000 LM_TRACE [17324] cmd0265D2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 10:05:15.410000 [7456] LM_TRACE Exit Udp data dispatcher
++++++[24332] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Fri Jun 20 2025 10:15:29.039000 [21136] LM_TRACE Start to parse argument
Fri Jun 20 2025 10:15:29.055000 [21136] LM_TRACE Parse parameter 17872 47.101.217.129:3080 56358 85960934
++++++[16588] Start log thread
[16588] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17872 -s 47.101.217.129:3080 -p 56358 -u 85960934
Fri Jun 20 2025 10:15:29.432000 [21136] LM_DEBUG Service port: 62846
Fri Jun 20 2025 10:15:29.433000 [21136] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 10:15:29.454000 (8092) AudioSocketEventTask started
++++++Fri Jun 20 2025 10:15:29.454000 (24328) ReactorEventTask started
Fri Jun 20 2025 10:15:30.684000 [21136] LM_TRACE Platform info 2130706433:56358
Fri Jun 20 2025 10:15:30.685000 [21136] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 10:15:31.099000 [24328] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 10:15:31.789000 [21136] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 10:15:31.789000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17872 -s 47.101.217.129:3080 -p 56358 -u 85960934
++++++Fri Jun 20 2025 10:15:32.269000 (15656) ProactorEventTask started
Fri Jun 20 2025 10:15:32.269000 [21136] Creating endpoint instance...
Fri Jun 20 2025 10:15:32.299000 [21136] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 10:15:32.299000 [21136] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 10:15:32.315000 [20712] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 10:15:32.315000 [23336] LM_TRACE Startting auido data processor
Fri Jun 20 2025 10:15:32.337000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 10:15:32.350000 [17328] Start command executor
Fri Jun 20 2025 10:15:32.350000 LM_TRACE [17328] cmd032CCE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:15:32.351000 [17328] LM_TRACE Set user info, sid: 35637234, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:15:32.351000 [17328] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 10:15:32.351000 [17328] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 10:15:32.351000 [17328] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 10:15:32.351000 LM_TRACE [17328] cmd032CC9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:15:32.352000 [17328] LM_TRACE User id: 85960934, sid: 35637234, tid: 0
Fri Jun 20 2025 10:15:32.352000 [17328] LM_TRACE Set user info, sid: 35637234, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:17:20.371000 [24328] LM_TRACE Process ID 17872, uid: 85960934
Fri Jun 20 2025 10:17:20.379000 LM_TRACE [17328] cmd032CD2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 10:17:21.784000 [20712] LM_TRACE Exit Udp data dispatcher++++++Fri Jun 20 2025 10:17:21.784000 [23336] LM_TRACE Stopping auido data processor

++++++[16588] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Fri Jun 20 2025 10:24:23.387000 [17148] LM_TRACE Start to parse argument
Fri Jun 20 2025 10:24:23.387000 [17148] LM_TRACE Parse parameter 15388 47.101.217.129:3080 53517 85960934
++++++[17592] Start log thread
[17592] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15388 -s 47.101.217.129:3080 -p 53517 -u 85960934
Fri Jun 20 2025 10:24:23.550000 [17148] LM_DEBUG Service port: 56061
Fri Jun 20 2025 10:24:23.551000 [17148] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 10:24:23.559000 (20216) AudioSocketEventTask started
++++++Fri Jun 20 2025 10:24:23.559000 (10232) ReactorEventTask started
Fri Jun 20 2025 10:24:26.839000 [17148] LM_TRACE Platform info 2130706433:53517
Fri Jun 20 2025 10:24:26.855000 [17148] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 10:24:26.855000 [10232] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 10:24:29.355000 [17148] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 10:24:29.355000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15388 -s 47.101.217.129:3080 -p 53517 -u 85960934
++++++Fri Jun 20 2025 10:24:31.948000 (10928) ProactorEventTask started
Fri Jun 20 2025 10:24:31.948000 [17148] Creating endpoint instance...
Fri Jun 20 2025 10:24:31.948000 [17148] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 10:24:31.948000 [17148] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 10:24:32.052000 [19844] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 10:24:32.052000 [21392] LM_TRACE Startting auido data processor
Fri Jun 20 2025 10:24:32.473000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 10:24:32.555000 [3504] Start command executor
Fri Jun 20 2025 10:24:32.555000 LM_TRACE [3504] cmd030ACE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:24:32.555000 [3504] LM_TRACE Set user info, sid: 36368843, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:24:32.555000 [3504] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 10:24:32.555000 [3504] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 10:24:32.555000 [3504] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 10:24:32.556000 LM_TRACE [3504] cmd030AC9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:24:32.556000 [3504] LM_TRACE User id: 85960934, sid: 36368843, tid: 0
Fri Jun 20 2025 10:24:32.556000 [3504] LM_TRACE Set user info, sid: 36368843, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:24:54.339000 [10232] LM_TRACE Process ID 15388, uid: 85960934
Fri Jun 20 2025 10:24:54.341000 LM_TRACE [3504] cmd030AD2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 10:24:59.364000 [19844] LM_TRACE Exit Udp data dispatcher
++++++Fri Jun 20 2025 10:24:59.364000 [21392] LM_TRACE Stopping auido data processor
++++++[17592] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[7944] Start log thread
[7944] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 2060 -s 47.101.217.129:3080 -p 61877 -u 85960934
Fri Jun 20 2025 10:37:15.537000 [15372] LM_TRACE Start to parse argument
Fri Jun 20 2025 10:37:15.556000 [15372] LM_TRACE Parse parameter 2060 47.101.217.129:3080 61877 85960934
Fri Jun 20 2025 10:37:16.079000 [15372] LM_DEBUG Service port: 54419
Fri Jun 20 2025 10:37:16.084000 [15372] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Fri Jun 20 2025 10:37:16.160000 (21976) AudioSocketEventTask started
++++++Fri Jun 20 2025 10:37:16.160000 (18332) ReactorEventTask started
Fri Jun 20 2025 10:37:17.867000 [15372] LM_TRACE Platform info 2130706433:61877
Fri Jun 20 2025 10:37:17.887000 [15372] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 10:37:17.889000 [18332] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 10:37:18.911000 [15372] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 10:37:18.912000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 2060 -s 47.101.217.129:3080 -p 61877 -u 85960934
++++++Fri Jun 20 2025 10:37:19.263000 (20368) ProactorEventTask started
Fri Jun 20 2025 10:37:19.338000 [15372] Creating endpoint instance...
Fri Jun 20 2025 10:37:19.468000 [15372] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 10:37:19.468000 [15372] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 10:37:19.493000 [17676] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 10:37:19.522000 [14784] LM_TRACE Startting auido data processor
Fri Jun 20 2025 10:37:19.621000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 10:37:19.651000 [17652] Start command executor
Fri Jun 20 2025 10:37:19.652000 LM_TRACE [17652] cmd031ECE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:37:19.652000 [17652] LM_TRACE Set user info, sid: 36835468, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:37:19.652000 [17652] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 10:37:19.652000 [17652] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 10:37:19.652000 [17652] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 10:37:19.652000 LM_TRACE [17652] cmd031EC9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 10:37:19.652000 [17652] LM_TRACE User id: 85960934, sid: 36835468, tid: 0
Fri Jun 20 2025 10:37:19.652000 [17652] LM_TRACE Set user info, sid: 36835468, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 10:39:36.112000 [18332] LM_TRACE Process ID 2060, uid: 85960934
Fri Jun 20 2025 10:39:36.112000 LM_TRACE [17652] cmd031ED2C0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jun 20 2025 10:39:38.924000 [17676] LM_TRACE Exit Udp data dispatcher
++++++[7944] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[24460] Start log thread
[24460] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 3956 -s 47.101.217.129:3080 -p 53981 -u 85960934
Fri Jun 20 2025 11:03:03.296000 [21104] LM_TRACE Start to parse argument
Fri Jun 20 2025 11:03:03.358000 [21104] LM_TRACE Parse parameter 3956 47.101.217.129:3080 53981 85960934
Fri Jun 20 2025 11:03:03.760000 [21104] LM_DEBUG Service port: 56135
++++++Fri Jun 20 2025 11:03:03.772000 (16832) AudioSocketEventTask started
++++++Fri Jun 20 2025 11:03:03.775000 (13968) ReactorEventTask started
Fri Jun 20 2025 11:03:03.775000 [21104] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Fri Jun 20 2025 11:03:04.390000 [21104] LM_TRACE Platform info 2130706433:53981
Fri Jun 20 2025 11:03:04.391000 [21104] LM_INFO Try to initialize volume mixer control(-1, -1)
'Fri Jun 20 2025 11:03:04.649000 [13968] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Fri Jun 20 2025 11:03:05.524000 [21104] LM_ERROR failed to call waveInOpen(1)
Fri Jun 20 2025 11:03:05.524000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 3956 -s 47.101.217.129:3080 -p 53981 -u 85960934
++++++Fri Jun 20 2025 11:03:06.013000 (2460) ProactorEventTask started
Fri Jun 20 2025 11:03:06.013000 [21104] Creating endpoint instance...
Fri Jun 20 2025 11:03:06.068000 [21104] LM_DEBUG Module "mod-tsx-layer" registered
Fri Jun 20 2025 11:03:06.068000 [21104] LM_DEBUG Module "mod-stateful-util" registered
++++++Fri Jun 20 2025 11:03:06.098000 [19508] LM_TRACE Start Udp data dispatcher
++++++Fri Jun 20 2025 11:03:06.112000 [20820] LM_TRACE Startting auido data processor
Fri Jun 20 2025 11:03:06.330000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Fri Jun 20 2025 11:03:06.359000 [10064] Start command executor
Fri Jun 20 2025 11:03:06.359000 LM_TRACE [10064] cmd0284CE50 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 11:03:06.359000 [10064] LM_TRACE Set user info, sid: 37711140, uid 85960934, tid: 0, game room id:0
Fri Jun 20 2025 11:03:06.359000 [10064] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Fri Jun 20 2025 11:03:06.359000 [10064] LM_TRACE CmdSetUserInfo update process info
Fri Jun 20 2025 11:03:06.359000 [10064] LM_TRACE CmdSetUserInfo execute successfully
Fri Jun 20 2025 11:03:06.360000 LM_TRACE [10064] cmd0284C9A0 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jun 20 2025 11:03:06.360000 [10064] LM_TRACE User id: 85960934, sid: 37711140, tid: 0
Fri Jun 20 2025 11:03:06.360000 [10064] LM_TRACE Set user info, sid: 37711140, uid 85960934, tid: 0, game room id:0
******** System Locale: Chinese (Simplified)_China.936
++++++[19664] Start log thread
[19664] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 16924 -s 47.101.217.129:3080 -p 60202 -u 85960934
Thu Jul 17 2025 20:29:09.431000 [1224] LM_TRACE Start to parse argument
Thu Jul 17 2025 20:29:09.431000 [1224] LM_TRACE Parse parameter 16924 47.101.217.129:3080 60202 85960934
Thu Jul 17 2025 20:29:09.530000 [1224] LM_DEBUG Service port: 57226
Thu Jul 17 2025 20:29:09.531000 [1224] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Thu Jul 17 2025 20:29:09.558000 (6952) AudioSocketEventTask started
++++++Thu Jul 17 2025 20:29:09.563000 (7684) ReactorEventTask started
Thu Jul 17 2025 20:29:11.381000 [1224] LM_TRACE Platform info 2130706433:60202
Thu Jul 17 2025 20:29:11.381000 [1224] LM_INFO Try to initialize volume mixer control(-1, -1)
'Thu Jul 17 2025 20:29:11.403000 [7684] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Thu Jul 17 2025 20:29:15.087000 [1224] LM_ERROR failed to call waveInOpen(1)
Thu Jul 17 2025 20:29:15.087000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 16924 -s 47.101.217.129:3080 -p 60202 -u 85960934
++++++Thu Jul 17 2025 20:29:16.071000 (12004) ProactorEventTask started
Thu Jul 17 2025 20:29:16.071000 [1224] Creating endpoint instance...
Thu Jul 17 2025 20:29:16.071000 [1224] LM_DEBUG Module "mod-tsx-layer" registered
Thu Jul 17 2025 20:29:16.072000 [1224] LM_DEBUG Module "mod-stateful-util" registered
++++++Thu Jul 17 2025 20:29:16.181000 [15852] LM_TRACE Start Udp data dispatcher
++++++Thu Jul 17 2025 20:29:16.186000 [17604] LM_TRACE Startting auido data processor
Thu Jul 17 2025 20:29:16.712000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Thu Jul 17 2025 20:29:16.730000 [20296] Start command executor
Thu Jul 17 2025 20:29:16.730000 LM_TRACE [20296] cmd00E9CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Thu Jul 17 2025 20:29:16.730000 [20296] LM_TRACE Set user info, sid: 54565828, uid 85960934, tid: 0, game room id:0
Thu Jul 17 2025 20:29:16.730000 [20296] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Thu Jul 17 2025 20:29:16.731000 [20296] LM_TRACE CmdSetUserInfo update process info
Thu Jul 17 2025 20:29:16.731000 [20296] LM_TRACE CmdSetUserInfo execute successfully
Thu Jul 17 2025 20:29:16.731000 LM_TRACE [20296] cmd00E9C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Thu Jul 17 2025 20:29:16.731000 [20296] LM_TRACE User id: 85960934, sid: 54565828, tid: 0
Thu Jul 17 2025 20:29:16.731000 [20296] LM_TRACE Set user info, sid: 54565828, uid 85960934, tid: 0, game room id:0
Fri Jul 18 2025 04:19:54.011000 LM_TRACE [20296] cmd00E9D2A0 Execute CMD_CP_PLAT2SERVICE_PLATFORMQUIT from CMD_CROSSPROCESS_7FSERVICE_PARAM
Fri Jul 18 2025 06:43:49.714000 [7684] LM_TRACE Process ID 16924, uid: 85960934
Fri Jul 18 2025 06:43:49.749000 LM_TRACE [20296] cmd00E9D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Fri Jul 18 2025 06:43:55.401000 [15852] LM_TRACE Exit Udp data dispatcher
++++++[19664] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Sat Jul 19 2025 02:16:52.659000 [4228] LM_TRACE Start to parse argument
Sat Jul 19 2025 02:16:52.660000 [4228] LM_TRACE Parse parameter 17072 47.101.217.129:3080 62880 85960934
++++++[14288] Start log thread
[14288] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17072 -s 47.101.217.129:3080 -p 62880 -u 85960934
Sat Jul 19 2025 02:16:53.118000 [4228] LM_DEBUG Service port: 54155
Sat Jul 19 2025 02:16:53.119000 [4228] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sat Jul 19 2025 02:16:53.149000 (17980) AudioSocketEventTask started
++++++Sat Jul 19 2025 02:16:53.149000 (13112) ReactorEventTask started
Sat Jul 19 2025 02:16:55.716000 [4228] LM_TRACE Platform info 2130706433:62880
Sat Jul 19 2025 02:16:55.716000 [4228] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 19 2025 02:16:55.902000 [13112] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 19 2025 02:16:56.868000 [4228] LM_ERROR failed to call waveInOpen(1)
Sat Jul 19 2025 02:16:56.869000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17072 -s 47.101.217.129:3080 -p 62880 -u 85960934
++++++Sat Jul 19 2025 02:16:57.045000 (4632) ProactorEventTask started
Sat Jul 19 2025 02:16:57.046000 [4228] Creating endpoint instance...
Sat Jul 19 2025 02:16:57.131000 [4228] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 19 2025 02:16:57.131000 [4228] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 19 2025 02:16:57.149000 [8220] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 19 2025 02:16:57.149000 [7896] LM_TRACE Startting auido data processor
Sat Jul 19 2025 02:16:57.196000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 19 2025 02:16:57.196000 [15356] Start command executor
Sat Jul 19 2025 02:16:57.197000 LM_TRACE [15356] cmd0252CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 19 2025 02:16:57.197000 [15356] LM_TRACE Set user info, sid: 1537046, uid 85960934, tid: 0, game room id:0
Sat Jul 19 2025 02:16:57.197000 [15356] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 19 2025 02:16:57.197000 [15356] LM_TRACE CmdSetUserInfo update process info
Sat Jul 19 2025 02:16:57.197000 [15356] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 19 2025 02:16:57.197000 LM_TRACE [15356] cmd0252C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 19 2025 02:16:57.197000 [15356] LM_TRACE User id: 85960934, sid: 1537046, tid: 0
Sat Jul 19 2025 02:16:57.197000 [15356] LM_TRACE Set user info, sid: 1537046, uid 85960934, tid: 0, game room id:0
Sat Jul 19 2025 02:18:19.323000 [13112] LM_TRACE Process ID 17072, uid: 85960934
Sat Jul 19 2025 02:18:19.323000 LM_TRACE [15356] cmd0252D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sat Jul 19 2025 02:18:26.875000 [8220] LM_TRACE Exit Udp data dispatcher++++++Sat Jul 19 2025 02:18:26.875000 [7896] LM_TRACE Stopping auido data processor

++++++[14288] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Sat Jul 19 2025 02:19:04.522000 [15340] LM_TRACE Start to parse argument
Sat Jul 19 2025 02:19:04.522000 [15340] LM_TRACE Parse parameter 2392 47.101.217.129:3080 63775 85960934
++++++[10592] Start log thread
[10592] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 2392 -s 47.101.217.129:3080 -p 63775 -u 85960934
Sat Jul 19 2025 02:19:04.646000 [15340] LM_DEBUG Service port: 50890
Sat Jul 19 2025 02:19:04.647000 [15340] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sat Jul 19 2025 02:19:04.692000 (252) AudioSocketEventTask started
++++++Sat Jul 19 2025 02:19:04.692000 (16212) ReactorEventTask started
Sat Jul 19 2025 02:19:05.130000 [15340] LM_TRACE Platform info 2130706433:63775
Sat Jul 19 2025 02:19:05.130000 [15340] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 19 2025 02:19:05.130000 [16212] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 19 2025 02:19:06.040000 [15340] LM_ERROR failed to call waveInOpen(1)
Sat Jul 19 2025 02:19:06.040000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 2392 -s 47.101.217.129:3080 -p 63775 -u 85960934
++++++Sat Jul 19 2025 02:19:06.178000 (10836) ProactorEventTask started
Sat Jul 19 2025 02:19:06.178000 [15340] Creating endpoint instance...
Sat Jul 19 2025 02:19:06.178000 [15340] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 19 2025 02:19:06.178000 [15340] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 19 2025 02:19:06.179000 [11116] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 19 2025 02:19:06.179000 [11284] LM_TRACE Startting auido data processor
Sat Jul 19 2025 02:19:06.204000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 19 2025 02:19:06.205000 [18036] Start command executor
Sat Jul 19 2025 02:19:06.205000 LM_TRACE [18036] cmd0101CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 19 2025 02:19:06.205000 [18036] LM_TRACE Set user info, sid: 8882109, uid 85960934, tid: 0, game room id:0
Sat Jul 19 2025 02:19:06.205000 [18036] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 19 2025 02:19:06.205000 [18036] LM_TRACE CmdSetUserInfo update process info
Sat Jul 19 2025 02:19:06.205000 [18036] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 19 2025 02:19:06.205000 LM_TRACE [18036] cmd0101C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 19 2025 02:19:06.205000 [18036] LM_TRACE User id: 85960934, sid: 8882109, tid: 0
Sat Jul 19 2025 02:19:06.205000 [18036] LM_TRACE Set user info, sid: 8882109, uid 85960934, tid: 0, game room id:0
Sat Jul 19 2025 03:24:41.695000 [16212] LM_TRACE Process ID 2392, uid: 85960934
Sat Jul 19 2025 03:24:41.761000 LM_TRACE [18036] cmd0101D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sat Jul 19 2025 03:24:46.335000 [11116] LM_TRACE Exit Udp data dispatcher
++++++[10592] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[800] Start log thread
[800] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 16900 -s 47.101.217.129:3080 -p 50384 -u 85960934
Tue Jul 22 2025 02:13:24.324000 [11492] LM_TRACE Start to parse argument
Tue Jul 22 2025 02:13:24.324000 [11492] LM_TRACE Parse parameter 16900 47.101.217.129:3080 50384 85960934
Tue Jul 22 2025 02:13:24.542000 [11492] LM_DEBUG Service port: 52219
Tue Jul 22 2025 02:13:24.543000 [11492] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Tue Jul 22 2025 02:13:24.560000 (2904) AudioSocketEventTask started
++++++Tue Jul 22 2025 02:13:24.560000 (16696) ReactorEventTask started
Tue Jul 22 2025 02:13:25.163000 [11492] LM_TRACE Platform info 2130706433:50384
Tue Jul 22 2025 02:13:25.164000 [11492] LM_INFO Try to initialize volume mixer control(-1, -1)
'Tue Jul 22 2025 02:13:25.565000 [16696] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 02:13:27.121000 [11492] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 02:13:27.121000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 16900 -s 47.101.217.129:3080 -p 50384 -u 85960934
++++++Tue Jul 22 2025 02:13:28.209000 (3492) ProactorEventTask started
Tue Jul 22 2025 02:13:28.209000 [11492] Creating endpoint instance...
Tue Jul 22 2025 02:13:28.209000 [11492] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 02:13:28.209000 [11492] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 02:13:28.275000 [4056] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 02:13:28.275000 [13564] LM_TRACE Startting auido data processor
Tue Jul 22 2025 02:13:28.312000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 02:13:28.375000 [17848] Start command executor
Tue Jul 22 2025 02:13:28.375000 LM_TRACE [17848] cmd02BACE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 02:13:28.375000 [17848] LM_TRACE Set user info, sid: 7106531, uid 85960934, tid: 0, game room id:0
Tue Jul 22 2025 02:13:28.375000 [17848] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 02:13:28.375000 [17848] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 02:13:28.375000 [17848] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 02:13:28.375000 LM_TRACE [17848] cmd02BAC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 02:13:28.375000 [17848] LM_TRACE User id: 85960934, sid: 7106531, tid: 0
Tue Jul 22 2025 02:13:28.375000 [17848] LM_TRACE Set user info, sid: 7106531, uid 85960934, tid: 0, game room id:0
Unknown command id 4194
Tue Jul 22 2025 09:21:48.123000 [16696] LM_TRACE Process ID 16900, uid: 85960934
Tue Jul 22 2025 09:21:48.148000 LM_TRACE [17848] cmd02BAD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Tue Jul 22 2025 09:21:57.927000 [4056] LM_TRACE Exit Udp data dispatcher
++++++[800] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[15052] Start log thread
[15052] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 7340 -s 47.101.217.129:3080 -p 49665 -u 90042332
Tue Jul 22 2025 17:49:19.979000 [14380] LM_TRACE Start to parse argument
Tue Jul 22 2025 17:49:19.979000 [14380] LM_TRACE Parse parameter 7340 47.101.217.129:3080 49665 90042332
Tue Jul 22 2025 17:49:19.987000 [14380] LM_DEBUG Service port: 63240
++++++Tue Jul 22 2025 17:49:19.987000 (8960) AudioSocketEventTask started
++++++Tue Jul 22 2025 17:49:19.987000 (4452) ReactorEventTask started
Tue Jul 22 2025 17:49:19.987000 [14380] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Tue Jul 22 2025 17:49:20.591000 [14380] LM_TRACE Platform info 2130706433:49665
Tue Jul 22 2025 17:49:20.591000 [14380] LM_INFO Try to initialize volume mixer control(-1, -1)
Tue Jul 22 2025 17:49:20.703000 [14380] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 17:49:20.703000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 7340 -s 47.101.217.129:3080 -p 49665 -u 90042332
++++++Tue Jul 22 2025 17:49:20.766000 (14644) ProactorEventTask started
Tue Jul 22 2025 17:49:20.766000 [14380] Creating endpoint instance...
Tue Jul 22 2025 17:49:20.766000 [14380] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 17:49:20.766000 [14380] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 17:49:20.766000 [11304] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 17:49:20.768000 [8720] LM_TRACE Startting auido data processor
Tue Jul 22 2025 17:49:20.778000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 17:49:20.778000 [13420] Start command executor
'Tue Jul 22 2025 17:49:23.117000 [4452] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 17:49:23.117000 LM_TRACE [13420] cmd02BCCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 17:49:23.117000 [13420] LM_TRACE Set user info, sid: 540406, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 17:49:23.117000 [13420] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 17:49:23.117000 [13420] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 17:49:23.117000 [13420] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 17:49:23.117000 LM_TRACE [13420] cmd02BCC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 17:49:23.118000 [13420] LM_TRACE User id: 90042332, sid: 540406, tid: 0
Tue Jul 22 2025 17:49:23.118000 [13420] LM_TRACE Set user info, sid: 540406, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 17:49:35.653000 [4452] LM_TRACE Process ID 7340, uid: 90042332
Tue Jul 22 2025 17:49:35.672000 LM_TRACE [13420] cmd02BCD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Tue Jul 22 2025 17:49:40.705000 [11304] LM_TRACE Exit Udp data dispatcher
++++++[15052] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Tue Jul 22 2025 17:52:32.355000 [17160] LM_TRACE Start to parse argument
Tue Jul 22 2025 17:52:32.355000 [17160] LM_TRACE Parse parameter 11096 47.101.217.129:3080 51381 90042332
++++++[16432] Start log thread
[16432] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 11096 -s 47.101.217.129:3080 -p 51381 -u 90042332
Tue Jul 22 2025 17:52:32.435000 [17160] LM_DEBUG Service port: 54999
Tue Jul 22 2025 17:52:32.437000 [17160] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Tue Jul 22 2025 17:52:32.452000 (15172) AudioSocketEventTask started
++++++Tue Jul 22 2025 17:52:32.455000 (6160) ReactorEventTask started
Tue Jul 22 2025 17:52:33.495000 [17160] LM_TRACE Platform info 2130706433:51381
Tue Jul 22 2025 17:52:33.495000 [17160] LM_INFO Try to initialize volume mixer control(-1, -1)
'Tue Jul 22 2025 17:52:33.496000 [6160] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 17:52:33.915000 [17160] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 17:52:33.915000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 11096 -s 47.101.217.129:3080 -p 51381 -u 90042332
++++++Tue Jul 22 2025 17:52:34.101000 (16312) ProactorEventTask started
Tue Jul 22 2025 17:52:34.102000 [17160] Creating endpoint instance...
Tue Jul 22 2025 17:52:34.102000 [17160] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 17:52:34.102000 [17160] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 17:52:34.102000 [16168] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 17:52:34.102000 [7340] LM_TRACE Startting auido data processor
Tue Jul 22 2025 17:52:34.123000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 17:52:34.123000 [15016] Start command executor
Tue Jul 22 2025 17:52:34.124000 LM_TRACE [15016] cmd0281CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 17:52:34.124000 [15016] LM_TRACE Set user info, sid: 951703, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 17:52:34.124000 [15016] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 17:52:34.126000 [15016] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 17:52:34.126000 [15016] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 17:52:34.126000 LM_TRACE [15016] cmd0281C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 17:52:34.126000 [15016] LM_TRACE User id: 90042332, sid: 951703, tid: 0
Tue Jul 22 2025 17:52:34.127000 [15016] LM_TRACE Set user info, sid: 951703, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 17:58:54.193000 [6160] LM_TRACE Process ID 11096, uid: 90042332
Tue Jul 22 2025 17:58:54.240000 LM_TRACE [15016] cmd0281D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Tue Jul 22 2025 17:59:04.321000 [16168] LM_TRACE Exit Udp data dispatcher
++++++[16432] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[4424] Start log thread
[4424] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14492 -s 47.101.217.129:3080 -p 61851 -u 90042332
Tue Jul 22 2025 18:14:10.492000 [8716] LM_TRACE Start to parse argument
Tue Jul 22 2025 18:14:10.530000 [8716] LM_TRACE Parse parameter 14492 47.101.217.129:3080 61851 90042332
Tue Jul 22 2025 18:14:10.783000 [8716] LM_DEBUG Service port: 62972
++++++Tue Jul 22 2025 18:14:10.783000 (9524) AudioSocketEventTask started
++++++Tue Jul 22 2025 18:14:10.783000 (16820) ReactorEventTask started
Tue Jul 22 2025 18:14:10.784000 [8716] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Tue Jul 22 2025 18:14:11.628000 [8716] LM_TRACE Platform info 2130706433:61851
Tue Jul 22 2025 18:14:11.629000 [8716] LM_INFO Try to initialize volume mixer control(-1, -1)
'Tue Jul 22 2025 18:14:11.629000 [16820] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 18:14:11.785000 [8716] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 18:14:11.785000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14492 -s 47.101.217.129:3080 -p 61851 -u 90042332
++++++Tue Jul 22 2025 18:14:11.855000 (12348) ProactorEventTask started
Tue Jul 22 2025 18:14:11.855000 [8716] Creating endpoint instance...
Tue Jul 22 2025 18:14:11.913000 [8716] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 18:14:11.913000 [8716] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 18:14:11.913000 [15412] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 18:14:11.915000 [7388] LM_TRACE Startting auido data processor
Tue Jul 22 2025 18:14:11.926000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 18:14:11.926000 [16664] Start command executor
Tue Jul 22 2025 18:14:11.926000 LM_TRACE [16664] cmd02ACCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 18:14:11.928000 [16664] LM_TRACE Set user info, sid: 1516312, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 18:14:11.928000 [16664] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 18:14:11.928000 [16664] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 18:14:11.928000 [16664] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 18:14:11.928000 LM_TRACE [16664] cmd02ACC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 18:14:11.928000 [16664] LM_TRACE User id: 90042332, sid: 1516312, tid: 0
Tue Jul 22 2025 18:14:11.928000 [16664] LM_TRACE Set user info, sid: 1516312, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 18:14:30.056000 [16820] LM_TRACE Process ID 14492, uid: 90042332
Tue Jul 22 2025 18:14:30.056000 LM_TRACE [16664] cmd02ACD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Tue Jul 22 2025 18:14:31.778000 [15412] LM_TRACE Exit Udp data dispatcher
++++++Tue Jul 22 2025 18:14:31.778000 [7388] LM_TRACE Stopping auido data processor
++++++[4424] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[1816] Start log thread
[1816] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14008 -s 47.101.217.129:3080 -p 60684 -u 90042332
Tue Jul 22 2025 18:28:58.755000 [6960] LM_TRACE Start to parse argument
Tue Jul 22 2025 18:28:58.755000 [6960] LM_TRACE Parse parameter 14008 47.101.217.129:3080 60684 90042332
Tue Jul 22 2025 18:28:58.774000 [6960] LM_DEBUG Service port: 61426
Tue Jul 22 2025 18:28:58.774000 [6960] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Tue Jul 22 2025 18:28:58.776000 (16264) AudioSocketEventTask started
++++++Tue Jul 22 2025 18:28:58.777000 (12580) ReactorEventTask started
Tue Jul 22 2025 18:28:58.902000 [6960] LM_TRACE Platform info 2130706433:60684
Tue Jul 22 2025 18:28:58.903000 [6960] LM_INFO Try to initialize volume mixer control(-1, -1)
'Tue Jul 22 2025 18:28:58.905000 [12580] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 18:28:59.373000 [6960] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 18:28:59.373000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14008 -s 47.101.217.129:3080 -p 60684 -u 90042332
++++++Tue Jul 22 2025 18:28:59.610000 (12100) ProactorEventTask started
Tue Jul 22 2025 18:28:59.610000 [6960] Creating endpoint instance...
Tue Jul 22 2025 18:28:59.669000 [6960] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 18:28:59.669000 [6960] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 18:28:59.671000 [13272] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 18:28:59.671000 [17072] LM_TRACE Startting auido data processor
Tue Jul 22 2025 18:28:59.692000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 18:28:59.692000 [7324] Start command executor
Tue Jul 22 2025 18:28:59.693000 LM_TRACE [7324] cmd027ECE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 18:28:59.693000 [7324] LM_TRACE Set user info, sid: 370296, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 18:28:59.693000 [7324] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 18:28:59.695000 [7324] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 18:28:59.695000 [7324] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 18:28:59.695000 LM_TRACE [7324] cmd027EC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 18:28:59.695000 [7324] LM_TRACE User id: 90042332, sid: 370296, tid: 0
Tue Jul 22 2025 18:28:59.695000 [7324] LM_TRACE Set user info, sid: 370296, uid 90042332, tid: 0, game room id:0
Tue Jul 22 2025 18:29:48.007000 [12580] LM_TRACE Process ID 14008, uid: 90042332
Tue Jul 22 2025 18:29:48.027000 LM_TRACE [7324] cmd027ED2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Tue Jul 22 2025 18:29:49.402000 [13272] LM_TRACE Exit Udp data dispatcher
++++++[1816] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[5052] Start log thread
[5052] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 1144 -s 47.101.217.129:3080 -p 53347 -u 85960934
Tue Jul 22 2025 18:56:03.913000 [12152] LM_TRACE Start to parse argument
Tue Jul 22 2025 18:56:03.956000 [12152] LM_TRACE Parse parameter 1144 47.101.217.129:3080 53347 85960934
Tue Jul 22 2025 18:56:04.228000 [12152] LM_DEBUG Service port: 57180
++++++Tue Jul 22 2025 18:56:04.228000 (10884) AudioSocketEventTask started
++++++Tue Jul 22 2025 18:56:04.228000 (17140) ReactorEventTask started
Tue Jul 22 2025 18:56:04.228000 [12152] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Tue Jul 22 2025 18:56:05.050000 [12152] LM_TRACE Platform info 2130706433:53347
Tue Jul 22 2025 18:56:05.050000 [12152] LM_INFO Try to initialize volume mixer control(-1, -1)
'Tue Jul 22 2025 18:56:05.052000 [17140] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 18:56:05.528000 [12152] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 18:56:05.528000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 1144 -s 47.101.217.129:3080 -p 53347 -u 85960934
++++++Tue Jul 22 2025 18:56:05.660000 (15016) ProactorEventTask started
Tue Jul 22 2025 18:56:05.660000 [12152] Creating endpoint instance...
Tue Jul 22 2025 18:56:05.690000 [12152] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 18:56:05.690000 [12152] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 18:56:05.690000 [13580] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 18:56:05.691000 [16768] LM_TRACE Startting auido data processor
Tue Jul 22 2025 18:56:05.714000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 18:56:05.714000 [11996] Start command executor
Tue Jul 22 2025 18:56:05.721000 LM_TRACE [11996] cmd0304CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 18:56:05.722000 [11996] LM_TRACE Set user info, sid: 2213796, uid 85960934, tid: 0, game room id:0
Tue Jul 22 2025 18:56:05.722000 [11996] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 18:56:05.722000 [11996] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 18:56:05.722000 [11996] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 18:56:05.722000 LM_TRACE [11996] cmd0304C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 18:56:05.722000 [11996] LM_TRACE User id: 85960934, sid: 2213796, tid: 0
Tue Jul 22 2025 18:56:05.722000 [11996] LM_TRACE Set user info, sid: 2213796, uid 85960934, tid: 0, game room id:0
******** System Locale: Chinese (Simplified)_China.936
++++++[12088] Start log thread
[12088] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 8840 -s 47.101.217.129:3080 -p 53085 -u 85960934
Tue Jul 22 2025 19:54:39.909000 [12952] LM_TRACE Start to parse argument
Tue Jul 22 2025 19:54:39.994000 [12952] LM_TRACE Parse parameter 8840 47.101.217.129:3080 53085 85960934
Tue Jul 22 2025 19:54:40.344000 [12952] LM_DEBUG Service port: 52705
++++++Tue Jul 22 2025 19:54:40.344000 (15516) AudioSocketEventTask started
++++++Tue Jul 22 2025 19:54:40.345000 (14744) ReactorEventTask started
Tue Jul 22 2025 19:54:40.345000 [12952] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Tue Jul 22 2025 19:54:40.773000 [12952] LM_TRACE Platform info 2130706433:53085
Tue Jul 22 2025 19:54:40.773000 [12952] LM_INFO Try to initialize volume mixer control(-1, -1)
'Tue Jul 22 2025 19:54:40.774000 [14744] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Tue Jul 22 2025 19:54:40.987000 [12952] LM_ERROR failed to call waveInOpen(1)
Tue Jul 22 2025 19:54:40.987000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 8840 -s 47.101.217.129:3080 -p 53085 -u 85960934
++++++Tue Jul 22 2025 19:54:41.054000 (16008) ProactorEventTask started
Tue Jul 22 2025 19:54:41.054000 [12952] Creating endpoint instance...
Tue Jul 22 2025 19:54:41.089000 [12952] LM_DEBUG Module "mod-tsx-layer" registered
Tue Jul 22 2025 19:54:41.089000 [12952] LM_DEBUG Module "mod-stateful-util" registered
++++++Tue Jul 22 2025 19:54:41.089000 [15428] LM_TRACE Start Udp data dispatcher
++++++Tue Jul 22 2025 19:54:41.089000 [12128] LM_TRACE Startting auido data processor
Tue Jul 22 2025 19:54:41.101000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Tue Jul 22 2025 19:54:41.101000 [12248] Start command executor
Tue Jul 22 2025 19:54:41.101000 LM_TRACE [12248] cmd0323CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 19:54:41.101000 [12248] LM_TRACE Set user info, sid: 252359, uid 85960934, tid: 0, game room id:0
Tue Jul 22 2025 19:54:41.101000 [12248] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Tue Jul 22 2025 19:54:41.101000 [12248] LM_TRACE CmdSetUserInfo update process info
Tue Jul 22 2025 19:54:41.101000 [12248] LM_TRACE CmdSetUserInfo execute successfully
Tue Jul 22 2025 19:54:41.101000 LM_TRACE [12248] cmd0323C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Tue Jul 22 2025 19:54:41.101000 [12248] LM_TRACE User id: 85960934, sid: 252359, tid: 0
Tue Jul 22 2025 19:54:41.101000 [12248] LM_TRACE Set user info, sid: 252359, uid 85960934, tid: 0, game room id:0
Tue Jul 22 2025 20:42:07.173000 [14744] LM_TRACE Process ID 8840, uid: 85960934
Tue Jul 22 2025 20:42:07.216000 LM_TRACE [12248] cmd0323D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Tue Jul 22 2025 20:42:11.900000 [15428] LM_TRACE Exit Udp data dispatcher
++++++[12088] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[12836] Start log thread
[12836] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14472 -s 47.101.217.129:3080 -p 65483 -u 85960934
Wed Jul 23 2025 03:41:51.970000 [2404] LM_TRACE Start to parse argument
Wed Jul 23 2025 03:41:52.057000 [2404] LM_TRACE Parse parameter 14472 47.101.217.129:3080 65483 85960934
Wed Jul 23 2025 03:41:52.406000 [2404] LM_DEBUG Service port: 64872
++++++Wed Jul 23 2025 03:41:52.406000 (19340) AudioSocketEventTask started
++++++Wed Jul 23 2025 03:41:52.406000 (17972) ReactorEventTask started
Wed Jul 23 2025 03:41:52.406000 [2404] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Wed Jul 23 2025 03:41:53.433000 [2404] LM_TRACE Platform info 2130706433:65483
Wed Jul 23 2025 03:41:53.486000 [2404] LM_INFO Try to initialize volume mixer control(-1, -1)
Wed Jul 23 2025 03:41:53.619000 [2404] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 03:41:53.619000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14472 -s 47.101.217.129:3080 -p 65483 -u 85960934
++++++Wed Jul 23 2025 03:41:53.708000 (17780) ProactorEventTask started
Wed Jul 23 2025 03:41:53.709000 [2404] Creating endpoint instance...
Wed Jul 23 2025 03:41:53.741000 [2404] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 03:41:53.741000 [2404] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 03:41:53.741000 [1748] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 03:41:53.742000 [15564] LM_TRACE Startting auido data processor
Wed Jul 23 2025 03:41:53.758000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 03:41:53.758000 [18684] Start command executor
'Wed Jul 23 2025 03:41:54.103000 [17972] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 03:41:54.103000 LM_TRACE [18684] cmd0303CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 03:41:54.103000 [18684] LM_TRACE Set user info, sid: 4022265, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 03:41:54.103000 [18684] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 03:41:54.103000 [18684] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 03:41:54.103000 [18684] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 03:41:54.103000 LM_TRACE [18684] cmd0303C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 03:41:54.103000 [18684] LM_TRACE User id: 85960934, sid: 4022265, tid: 0
Wed Jul 23 2025 03:41:54.103000 [18684] LM_TRACE Set user info, sid: 4022265, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 03:44:44.498000 [17972] LM_TRACE Process ID 14472, uid: 85960934
Wed Jul 23 2025 03:44:44.498000 LM_TRACE [18684] cmd0303D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 03:44:53.621000 [1748] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 03:44:53.621000 [15564] LM_TRACE Stopping auido data processor
++++++[12836] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[13016] Start log thread
[13016] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15976 -s 47.101.217.129:3080 -p 49913 -u 85960934
Wed Jul 23 2025 04:19:03.836000 [6152] LM_TRACE Start to parse argument
Wed Jul 23 2025 04:19:03.837000 [6152] LM_TRACE Parse parameter 15976 47.101.217.129:3080 49913 85960934
Wed Jul 23 2025 04:19:03.852000 [6152] LM_DEBUG Service port: 58831
Wed Jul 23 2025 04:19:03.852000 [6152] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 04:19:03.852000 (20212) AudioSocketEventTask started
++++++Wed Jul 23 2025 04:19:03.852000 (3468) ReactorEventTask started
Wed Jul 23 2025 04:19:04.002000 [6152] LM_TRACE Platform info 2130706433:49913
Wed Jul 23 2025 04:19:04.003000 [6152] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 04:19:04.003000 [3468] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 04:19:04.605000 [6152] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 04:19:04.605000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15976 -s 47.101.217.129:3080 -p 49913 -u 85960934
++++++Wed Jul 23 2025 04:19:04.861000 (4064) ProactorEventTask started
Wed Jul 23 2025 04:19:04.861000 [6152] Creating endpoint instance...
Wed Jul 23 2025 04:19:04.861000 [6152] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 04:19:04.861000 [6152] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 04:19:04.868000 [13496] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 04:19:04.868000 [18624] LM_TRACE Startting auido data processor
Wed Jul 23 2025 04:19:04.893000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 04:19:04.952000 [19616] Start command executor
Wed Jul 23 2025 04:19:04.952000 LM_TRACE [19616] cmd0293CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:19:04.952000 [19616] LM_TRACE Set user info, sid: 29867015, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:19:04.952000 [19616] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 04:19:04.952000 [19616] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 04:19:04.952000 [19616] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 04:19:04.952000 LM_TRACE [19616] cmd0293C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:19:04.952000 [19616] LM_TRACE User id: 85960934, sid: 29867015, tid: 0
Wed Jul 23 2025 04:19:04.952000 [19616] LM_TRACE Set user info, sid: 29867015, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:19:20.255000 [3468] LM_TRACE Process ID 15976, uid: 85960934
Wed Jul 23 2025 04:19:20.255000 LM_TRACE [19616] cmd0293D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 04:19:24.612000 [13496] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 04:19:24.612000 [18624] LM_TRACE Stopping auido data processor
++++++[13016] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 04:20:28.978000 [17340] LM_TRACE Start to parse argument
Wed Jul 23 2025 04:20:28.979000 [17340] LM_TRACE Parse parameter 9080 47.101.217.129:3080 56576 85960934
++++++[17820] Start log thread
[17820] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 9080 -s 47.101.217.129:3080 -p 56576 -u 85960934
Wed Jul 23 2025 04:20:29.025000 [17340] LM_DEBUG Service port: 64187
Wed Jul 23 2025 04:20:29.025000 [17340] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 04:20:29.037000 (16768) AudioSocketEventTask started
++++++Wed Jul 23 2025 04:20:29.037000 (17564) ReactorEventTask started
Wed Jul 23 2025 04:20:29.237000 [17340] LM_TRACE Platform info 2130706433:56576
Wed Jul 23 2025 04:20:29.237000 [17340] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 04:20:29.267000 [17564] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 04:20:29.359000 [17340] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 04:20:29.359000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 9080 -s 47.101.217.129:3080 -p 56576 -u 85960934
++++++Wed Jul 23 2025 04:20:29.451000 (14812) ProactorEventTask started
Wed Jul 23 2025 04:20:29.451000 [17340] Creating endpoint instance...
Wed Jul 23 2025 04:20:29.451000 [17340] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 04:20:29.451000 [17340] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 04:20:29.452000 [13104] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 04:20:29.452000 [17648] LM_TRACE Startting auido data processor
Wed Jul 23 2025 04:20:29.475000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 04:20:29.476000 [18624] Start command executor
Wed Jul 23 2025 04:20:29.481000 LM_TRACE [18624] cmd02E5CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:20:29.481000 [18624] LM_TRACE Set user info, sid: 31447203, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:20:29.481000 [18624] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 04:20:29.481000 [18624] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 04:20:29.481000 [18624] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 04:20:29.481000 LM_TRACE [18624] cmd02E5C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:20:29.481000 [18624] LM_TRACE User id: 85960934, sid: 31447203, tid: 0
Wed Jul 23 2025 04:20:29.482000 [18624] LM_TRACE Set user info, sid: 31447203, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:20:46.338000 [17564] LM_TRACE Process ID 9080, uid: 85960934
Wed Jul 23 2025 04:20:46.338000 LM_TRACE [18624] cmd02E5D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 04:20:49.362000 [13104] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 04:20:49.362000 [17648] LM_TRACE Stopping auido data processor
++++++[17820] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[8624] Start log thread
[8624] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20004 -s 47.101.217.129:3080 -p 56956 -u 85960934
Wed Jul 23 2025 04:56:16.156000 [16576] LM_TRACE Start to parse argument
Wed Jul 23 2025 04:56:16.156000 [16576] LM_TRACE Parse parameter 20004 47.101.217.129:3080 56956 85960934
Wed Jul 23 2025 04:56:16.181000 [16576] LM_DEBUG Service port: 61498
Wed Jul 23 2025 04:56:16.181000 [16576] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 04:56:16.185000 (17036) AudioSocketEventTask started
++++++Wed Jul 23 2025 04:56:16.185000 (17764) ReactorEventTask started
Wed Jul 23 2025 04:56:16.349000 [16576] LM_TRACE Platform info 2130706433:56956
Wed Jul 23 2025 04:56:16.349000 [16576] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 04:56:16.376000 [17764] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 04:56:16.948000 [16576] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 04:56:16.948000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20004 -s 47.101.217.129:3080 -p 56956 -u 85960934
++++++Wed Jul 23 2025 04:56:17.949000 (19752) ProactorEventTask started
Wed Jul 23 2025 04:56:17.949000 [16576] Creating endpoint instance...
Wed Jul 23 2025 04:56:17.949000 [16576] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 04:56:17.949000 [16576] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 04:56:17.987000 [19024] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 04:56:17.988000 [20396] LM_TRACE Startting auido data processor
Wed Jul 23 2025 04:56:18.227000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 04:56:18.243000 [14908] Start command executor
Wed Jul 23 2025 04:56:18.243000 LM_TRACE [14908] cmd0329CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:56:18.243000 [14908] LM_TRACE Set user info, sid: 31534218, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:56:18.243000 [14908] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 04:56:18.243000 [14908] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 04:56:18.243000 [14908] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 04:56:18.243000 LM_TRACE [14908] cmd0329C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:56:18.243000 [14908] LM_TRACE User id: 85960934, sid: 31534218, tid: 0
Wed Jul 23 2025 04:56:18.243000 [14908] LM_TRACE Set user info, sid: 31534218, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:56:22.916000 [17764] LM_TRACE Process ID 20004, uid: 85960934
Wed Jul 23 2025 04:56:22.916000 LM_TRACE [14908] cmd0329D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 04:56:26.953000 [19024] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 04:56:26.953000 [20396] LM_TRACE Stopping auido data processor
++++++[8624] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[20212] Start log thread
[20212] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 13016 -s 47.101.217.129:3080 -p 56409 -u 85960934
Wed Jul 23 2025 04:56:52.816000 [17428] LM_TRACE Start to parse argument
Wed Jul 23 2025 04:56:52.816000 [17428] LM_TRACE Parse parameter 13016 47.101.217.129:3080 56409 85960934
Wed Jul 23 2025 04:56:52.838000 [17428] LM_DEBUG Service port: 51923
Wed Jul 23 2025 04:56:52.838000 [17428] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 04:56:52.854000 (19460) AudioSocketEventTask started
++++++Wed Jul 23 2025 04:56:52.854000 (14840) ReactorEventTask started
Wed Jul 23 2025 04:56:52.919000 [17428] LM_TRACE Platform info 2130706433:56409
Wed Jul 23 2025 04:56:52.919000 [17428] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 04:56:52.933000 [14840] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 04:56:53.336000 [17428] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 04:56:53.336000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 13016 -s 47.101.217.129:3080 -p 56409 -u 85960934
++++++Wed Jul 23 2025 04:56:53.474000 (17580) ProactorEventTask started
Wed Jul 23 2025 04:56:53.474000 [17428] Creating endpoint instance...
Wed Jul 23 2025 04:56:53.474000 [17428] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 04:56:53.474000 [17428] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 04:56:53.480000 [5648] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 04:56:53.482000 [19556] LM_TRACE Startting auido data processor
Wed Jul 23 2025 04:56:53.490000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 04:56:53.491000 [4576] Start command executor
Wed Jul 23 2025 04:56:53.491000 LM_TRACE [4576] cmd0282CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:56:53.491000 [4576] LM_TRACE Set user info, sid: 33671015, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:56:53.491000 [4576] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 04:56:53.491000 [4576] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 04:56:53.491000 [4576] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 04:56:53.491000 LM_TRACE [4576] cmd0282C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 04:56:53.491000 [4576] LM_TRACE User id: 85960934, sid: 33671015, tid: 0
Wed Jul 23 2025 04:56:53.491000 [4576] LM_TRACE Set user info, sid: 33671015, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 04:57:02.747000 [14840] LM_TRACE Process ID 13016, uid: 85960934
Wed Jul 23 2025 04:57:02.747000 LM_TRACE [4576] cmd0282D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 04:57:03.333000 [5648] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 04:57:03.333000 [19556] LM_TRACE Stopping auido data processor
++++++[20212] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:09:13.546000 [16348] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:09:13.546000 [16348] LM_TRACE Parse parameter 9100 47.101.217.129:3080 53508 85960934
Wed Jul 23 2025 05:09:13.574000 [16348] LM_DEBUG Service port: 55849
Wed Jul 23 2025 05:09:13.574000 [16348] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++[5024] Start log thread
[5024] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 9100 -s 47.101.217.129:3080 -p 53508 -u 85960934
++++++Wed Jul 23 2025 05:09:13.589000 (18396) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:09:13.589000 (15000) ReactorEventTask started
Wed Jul 23 2025 05:09:13.769000 [16348] LM_TRACE Platform info 2130706433:53508
Wed Jul 23 2025 05:09:13.769000 [16348] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:09:13.791000 [15000] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:09:14.356000 [16348] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:09:14.356000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 9100 -s 47.101.217.129:3080 -p 53508 -u 85960934
++++++Wed Jul 23 2025 05:09:14.601000 (9080) ProactorEventTask started
Wed Jul 23 2025 05:09:14.601000 [16348] Creating endpoint instance...
Wed Jul 23 2025 05:09:14.601000 [16348] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:09:14.601000 [16348] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:09:14.632000 [14564] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:09:14.636000 [18948] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:09:14.685000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:09:14.696000 [17336] Start command executor
Wed Jul 23 2025 05:09:14.696000 LM_TRACE [17336] cmd02B6CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:09:14.696000 [17336] LM_TRACE Set user info, sid: 33710312, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:09:14.700000 [17336] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:09:14.700000 [17336] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:09:14.700000 [17336] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:09:14.700000 LM_TRACE [17336] cmd02B6C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:09:14.700000 [17336] LM_TRACE User id: 85960934, sid: 33710312, tid: 0
Wed Jul 23 2025 05:09:14.700000 [17336] LM_TRACE Set user info, sid: 33710312, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:09:25.202000 [15000] LM_TRACE Process ID 9100, uid: 85960934
Wed Jul 23 2025 05:09:25.202000 LM_TRACE [17336] cmd02B6D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:09:34.364000 [14564] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 05:09:34.364000 [18948] LM_TRACE Stopping auido data processor
++++++[5024] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[8084] Start log thread
[8084] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20332 -s 47.101.217.129:3080 -p 54784 -u 85960934
Wed Jul 23 2025 05:17:01.790000 [18652] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:17:01.836000 [18652] LM_TRACE Parse parameter 20332 47.101.217.129:3080 54784 85960934
Wed Jul 23 2025 05:17:02.106000 [18652] LM_DEBUG Service port: 54394
Wed Jul 23 2025 05:17:02.107000 [18652] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:17:02.111000 (17592) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:17:02.111000 (11324) ReactorEventTask started
Wed Jul 23 2025 05:17:02.788000 [18652] LM_TRACE Platform info 2130706433:54784
Wed Jul 23 2025 05:17:02.789000 [18652] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:17:02.789000 [11324] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:17:03.055000 [18652] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:17:03.055000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 20332 -s 47.101.217.129:3080 -p 54784 -u 85960934
++++++Wed Jul 23 2025 05:17:03.115000 (14976) ProactorEventTask started
Wed Jul 23 2025 05:17:03.115000 [18652] Creating endpoint instance...
Wed Jul 23 2025 05:17:03.153000 [18652] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:17:03.153000 [18652] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:17:03.154000 [7960] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:17:03.154000 [9428] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:17:03.165000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:17:03.166000 [17824] Start command executor
Wed Jul 23 2025 05:17:03.166000 LM_TRACE [17824] cmd0346CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:17:03.166000 [17824] LM_TRACE Set user info, sid: 34453031, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:17:03.166000 [17824] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:17:03.166000 [17824] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:17:03.166000 [17824] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:17:03.166000 LM_TRACE [17824] cmd0346C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:17:03.166000 [17824] LM_TRACE User id: 85960934, sid: 34453031, tid: 0
Wed Jul 23 2025 05:17:03.166000 [17824] LM_TRACE Set user info, sid: 34453031, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:17:07.377000 [11324] LM_TRACE Process ID 20332, uid: 85960934
Wed Jul 23 2025 05:17:07.377000 LM_TRACE [17824] cmd0346D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:17:13.044000 [7960] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 05:17:13.044000 [9428] LM_TRACE Stopping auido data processor
++++++[8084] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:27:00.335000 [18748] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:27:00.335000 [18748] LM_TRACE Parse parameter 5280 47.101.217.129:3080 54368 85960934
++++++[18612] Start log thread
[18612] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 5280 -s 47.101.217.129:3080 -p 54368 -u 85960934
Wed Jul 23 2025 05:27:00.387000 [18748] LM_DEBUG Service port: 51931
Wed Jul 23 2025 05:27:00.390000 [18748] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:27:00.394000 (19928) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:27:00.394000 (13660) ReactorEventTask started
Wed Jul 23 2025 05:27:00.581000 [18748] LM_TRACE Platform info 2130706433:54368
Wed Jul 23 2025 05:27:00.581000 [18748] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:27:00.585000 [13660] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:27:00.732000 [18748] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:27:00.733000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 5280 -s 47.101.217.129:3080 -p 54368 -u 85960934
++++++Wed Jul 23 2025 05:27:01.022000 (20072) ProactorEventTask started
Wed Jul 23 2025 05:27:01.022000 [18748] Creating endpoint instance...
Wed Jul 23 2025 05:27:01.022000 [18748] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:27:01.022000 [18748] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:27:01.054000 [18868] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:27:01.054000 [17796] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:27:01.100000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:27:01.173000 [19184] Start command executor
Wed Jul 23 2025 05:27:01.173000 LM_TRACE [19184] cmd0256CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:27:01.173000 [19184] LM_TRACE Set user info, sid: 34919875, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:27:01.173000 [19184] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:27:01.173000 [19184] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:27:01.173000 [19184] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:27:01.173000 LM_TRACE [19184] cmd0256C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:27:01.173000 [19184] LM_TRACE User id: 85960934, sid: 34919875, tid: 0
Wed Jul 23 2025 05:27:01.173000 [19184] LM_TRACE Set user info, sid: 34919875, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:27:16.897000 [13660] LM_TRACE Process ID 5280, uid: 85960934
Wed Jul 23 2025 05:27:16.897000 LM_TRACE [19184] cmd0256D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:27:20.737000 [18868] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 05:27:20.737000 [17796] LM_TRACE Stopping auido data processor
++++++[18612] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:35:08.860000 [16076] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:35:08.861000 [16076] LM_TRACE Parse parameter 16612 47.101.217.129:3080 64299 85960934
++++++[6112] Start log thread
[6112] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 16612 -s 47.101.217.129:3080 -p 64299 -u 85960934
Wed Jul 23 2025 05:35:08.889000 [16076] LM_DEBUG Service port: 57490
Wed Jul 23 2025 05:35:08.890000 [16076] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:35:08.898000 (19488) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:35:08.899000 (8280) ReactorEventTask started
Wed Jul 23 2025 05:35:09.046000 [16076] LM_TRACE Platform info 2130706433:64299
Wed Jul 23 2025 05:35:09.046000 [16076] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:35:09.131000 [8280] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:35:09.470000 [16076] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:35:09.470000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 16612 -s 47.101.217.129:3080 -p 64299 -u 85960934
++++++Wed Jul 23 2025 05:35:09.560000 (17356) ProactorEventTask started
Wed Jul 23 2025 05:35:09.560000 [16076] Creating endpoint instance...
Wed Jul 23 2025 05:35:09.560000 [16076] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:35:09.560000 [16076] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:35:09.562000 [15796] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:35:09.562000 [18308] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:35:09.578000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:35:09.578000 [20132] Start command executor
Wed Jul 23 2025 05:35:09.579000 LM_TRACE [20132] cmd024ACE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:35:09.579000 [20132] LM_TRACE Set user info, sid: 35524937, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:35:09.579000 [20132] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:35:09.579000 [20132] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:35:09.579000 [20132] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:35:09.580000 LM_TRACE [20132] cmd024AC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:35:09.580000 [20132] LM_TRACE User id: 85960934, sid: 35524937, tid: 0
Wed Jul 23 2025 05:35:09.580000 [20132] LM_TRACE Set user info, sid: 35524937, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:35:50.164000 [8280] LM_TRACE Process ID 16612, uid: 85960934
Wed Jul 23 2025 05:35:50.164000 LM_TRACE [20132] cmd024AD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:35:59.468000 [15796] LM_TRACE Exit Udp data dispatcher
++++++[6112] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:40:03.806000 [15952] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:40:03.806000 [15952] LM_TRACE Parse parameter 14712 47.101.217.129:3080 53044 85960934
++++++[15012] Start log thread
[15012] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14712 -s 47.101.217.129:3080 -p 53044 -u 85960934
Wed Jul 23 2025 05:40:03.898000 [15952] LM_DEBUG Service port: 54898
Wed Jul 23 2025 05:40:03.898000 [15952] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:40:03.909000 (6412) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:40:03.909000 (18336) ReactorEventTask started
Wed Jul 23 2025 05:40:04.143000 [15952] LM_TRACE Platform info 2130706433:53044
Wed Jul 23 2025 05:40:04.143000 [15952] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:40:04.153000 [18336] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:40:04.716000 [15952] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:40:04.716000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14712 -s 47.101.217.129:3080 -p 53044 -u 85960934
++++++Wed Jul 23 2025 05:40:04.978000 (9444) ProactorEventTask started
Wed Jul 23 2025 05:40:04.978000 [15952] Creating endpoint instance...
Wed Jul 23 2025 05:40:04.978000 [15952] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:40:04.978000 [15952] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:40:05.021000 [13764] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:40:05.023000 [15028] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:40:05.158000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:40:05.175000 [19416] Start command executor
Wed Jul 23 2025 05:40:05.175000 LM_TRACE [19416] cmd0301CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:40:05.175000 [19416] LM_TRACE Set user info, sid: 36036828, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:40:05.175000 [19416] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:40:05.175000 [19416] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:40:05.175000 [19416] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:40:05.175000 LM_TRACE [19416] cmd0301C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:40:05.175000 [19416] LM_TRACE User id: 85960934, sid: 36036828, tid: 0
Wed Jul 23 2025 05:40:05.175000 [19416] LM_TRACE Set user info, sid: 36036828, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:41:02.652000 [18336] LM_TRACE Process ID 14712, uid: 85960934
Wed Jul 23 2025 05:41:02.652000 LM_TRACE [19416] cmd0301D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:41:04.717000 [13764] LM_TRACE Exit Udp data dispatcher
++++++[15012] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:42:03.368000 [18836] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:42:03.368000 [18836] LM_TRACE Parse parameter 19480 47.101.217.129:3080 58431 90042332
++++++[18380] Start log thread
[18380] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 19480 -s 47.101.217.129:3080 -p 58431 -u 90042332
Wed Jul 23 2025 05:42:03.402000 [18836] LM_DEBUG Service port: 60975
Wed Jul 23 2025 05:42:03.403000 [18836] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:42:03.405000 (9672) ReactorEventTask started
++++++Wed Jul 23 2025 05:42:03.405000 (3044) AudioSocketEventTask started
Wed Jul 23 2025 05:42:03.602000 [18836] LM_TRACE Platform info 2130706433:58431
Wed Jul 23 2025 05:42:03.602000 [18836] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:42:03.608000 [9672] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:42:03.738000 [18836] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:42:03.739000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 19480 -s 47.101.217.129:3080 -p 58431 -u 90042332
++++++Wed Jul 23 2025 05:42:04.057000 (18832) ProactorEventTask started
Wed Jul 23 2025 05:42:04.057000 [18836] Creating endpoint instance...
Wed Jul 23 2025 05:42:04.057000 [18836] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:42:04.057000 [18836] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:42:04.090000 [5584] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:42:04.090000 [5684] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:42:04.104000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:42:04.158000 [6112] Start command executor
Wed Jul 23 2025 05:42:04.158000 LM_TRACE [6112] cmd02D8CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:42:04.158000 [6112] LM_TRACE Set user info, sid: 36352265, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 05:42:04.158000 [6112] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:42:04.158000 [6112] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:42:04.158000 [6112] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:42:04.159000 LM_TRACE [6112] cmd02D8C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:42:04.159000 [6112] LM_TRACE User id: 90042332, sid: 36352265, tid: 0
Wed Jul 23 2025 05:42:04.159000 [6112] LM_TRACE Set user info, sid: 36352265, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 05:42:45.258000 [9672] LM_TRACE Process ID 19480, uid: 90042332
Wed Jul 23 2025 05:42:45.258000 LM_TRACE [6112] cmd02D8D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:42:53.733000 [5584] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 05:42:53.733000 [5684] LM_TRACE Stopping auido data processor
++++++[18380] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[14880] Start log thread
[14880] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15716 -s 47.101.217.129:3080 -p 59712 -u 44809717
Wed Jul 23 2025 05:43:50.770000 [8900] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:43:50.770000 [8900] LM_TRACE Parse parameter 15716 47.101.217.129:3080 59712 44809717
Wed Jul 23 2025 05:43:50.783000 [8900] LM_DEBUG Service port: 64025
Wed Jul 23 2025 05:43:50.783000 [8900] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:43:50.787000 (18708) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:43:50.788000 (11952) ReactorEventTask started
Wed Jul 23 2025 05:43:50.847000 [8900] LM_TRACE Platform info 2130706433:59712
Wed Jul 23 2025 05:43:50.848000 [8900] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:43:50.922000 [11952] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:43:50.993000 [8900] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:43:50.993000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15716 -s 47.101.217.129:3080 -p 59712 -u 44809717
++++++Wed Jul 23 2025 05:43:51.085000 (19244) ProactorEventTask started
Wed Jul 23 2025 05:43:51.085000 [8900] Creating endpoint instance...
Wed Jul 23 2025 05:43:51.085000 [8900] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:43:51.085000 [8900] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:43:51.087000 [5232] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:43:51.091000 [17716] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:43:51.101000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:43:51.103000 [15792] Start command executor
Wed Jul 23 2025 05:43:51.116000 LM_TRACE [15792] cmd02AACE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:43:51.116000 [15792] LM_TRACE Set user info, sid: 36452046, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 05:43:51.116000 [15792] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:43:51.116000 [15792] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:43:51.116000 [15792] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:43:51.117000 LM_TRACE [15792] cmd02AAC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:43:51.117000 [15792] LM_TRACE User id: 44809717, sid: 36452046, tid: 0
Wed Jul 23 2025 05:43:51.117000 [15792] LM_TRACE Set user info, sid: 36452046, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 05:45:39.708000 [11952] LM_TRACE Process ID 15716, uid: 44809717
Wed Jul 23 2025 05:45:39.708000 LM_TRACE [15792] cmd02AAD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:45:40.979000 [5232] LM_TRACE Exit Udp data dispatcher
++++++[14880] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:48:53.724000 [13412] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:48:53.724000 [13412] LM_TRACE Parse parameter 14448 47.101.217.129:3080 58736 85960934
++++++[14572] Start log thread
[14572] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14448 -s 47.101.217.129:3080 -p 58736 -u 85960934
Wed Jul 23 2025 05:48:53.750000 [13412] LM_DEBUG Service port: 62420
Wed Jul 23 2025 05:48:53.750000 [13412] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:48:53.750000 (19060) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:48:53.752000 (4624) ReactorEventTask started
Wed Jul 23 2025 05:48:53.801000 [13412] LM_TRACE Platform info 2130706433:58736
Wed Jul 23 2025 05:48:53.802000 [13412] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:48:53.844000 [4624] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:48:53.882000 [13412] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:48:53.882000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14448 -s 47.101.217.129:3080 -p 58736 -u 85960934
++++++Wed Jul 23 2025 05:48:53.974000 (19716) ProactorEventTask started
Wed Jul 23 2025 05:48:53.974000 [13412] Creating endpoint instance...
Wed Jul 23 2025 05:48:53.974000 [13412] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:48:53.974000 [13412] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:48:53.974000 [16824] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:48:53.976000 [8572] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:48:53.984000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:48:53.984000 [13872] Start command executor
Wed Jul 23 2025 05:48:53.985000 LM_TRACE [13872] cmd027DCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:48:53.985000 [13872] LM_TRACE Set user info, sid: 36627187, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:48:53.985000 [13872] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:48:53.985000 [13872] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:48:53.985000 [13872] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:48:53.985000 LM_TRACE [13872] cmd027DC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:48:53.985000 [13872] LM_TRACE User id: 85960934, sid: 36627187, tid: 0
Wed Jul 23 2025 05:48:53.985000 [13872] LM_TRACE Set user info, sid: 36627187, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:49:35.499000 [4624] LM_TRACE Process ID 14448, uid: 85960934
Wed Jul 23 2025 05:49:35.499000 LM_TRACE [13872] cmd027DD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:49:43.879000 [16824] LM_TRACE Exit Udp data dispatcher
++++++[14572] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:50:34.354000 [19452] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:50:34.354000 [19452] LM_TRACE Parse parameter 11288 47.101.217.129:3080 57544 90042332
++++++[18472] Start log thread
[18472] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 11288 -s 47.101.217.129:3080 -p 57544 -u 90042332
Wed Jul 23 2025 05:50:34.377000 [19452] LM_DEBUG Service port: 64292
Wed Jul 23 2025 05:50:34.377000 [19452] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:50:34.389000 (17960) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:50:34.389000 (3920) ReactorEventTask started
Wed Jul 23 2025 05:50:34.483000 [19452] LM_TRACE Platform info 2130706433:57544
Wed Jul 23 2025 05:50:34.483000 [19452] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:50:34.485000 [3920] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:50:34.720000 [19452] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:50:34.721000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 11288 -s 47.101.217.129:3080 -p 57544 -u 90042332
++++++Wed Jul 23 2025 05:50:34.927000 (2764) ProactorEventTask started
Wed Jul 23 2025 05:50:34.927000 [19452] Creating endpoint instance...
Wed Jul 23 2025 05:50:34.927000 [19452] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:50:34.927000 [19452] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:50:34.935000 [16376] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:50:34.935000 [4272] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:50:34.966000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:50:34.966000 [14920] Start command executor
Wed Jul 23 2025 05:50:34.967000 LM_TRACE [14920] cmd02BDCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:50:34.967000 [14920] LM_TRACE Set user info, sid: 36862359, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 05:50:34.967000 [14920] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:50:34.967000 [14920] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:50:34.967000 [14920] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:50:34.967000 LM_TRACE [14920] cmd02BDC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:50:34.967000 [14920] LM_TRACE User id: 90042332, sid: 36862359, tid: 0
Wed Jul 23 2025 05:50:34.967000 [14920] LM_TRACE Set user info, sid: 36862359, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 05:51:16.205000 [3920] LM_TRACE Process ID 11288, uid: 90042332
Wed Jul 23 2025 05:51:16.205000 LM_TRACE [14920] cmd02BDD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:51:24.724000 [16376] LM_TRACE Exit Udp data dispatcher
++++++[18472] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 05:52:18.964000 [15036] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:52:18.964000 [15036] LM_TRACE Parse parameter 18708 47.101.217.129:3080 57145 44809717
++++++[14432] Start log thread
[14432] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18708 -s 47.101.217.129:3080 -p 57145 -u 44809717
Wed Jul 23 2025 05:52:19.024000 [15036] LM_DEBUG Service port: 51343
Wed Jul 23 2025 05:52:19.025000 [15036] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:52:19.040000 (13860) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:52:19.041000 (15972) ReactorEventTask started
Wed Jul 23 2025 05:52:19.123000 [15036] LM_TRACE Platform info 2130706433:57145
Wed Jul 23 2025 05:52:19.124000 [15036] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:52:19.233000 [15972] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:52:19.405000 [15036] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:52:19.405000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18708 -s 47.101.217.129:3080 -p 57145 -u 44809717
++++++Wed Jul 23 2025 05:52:19.483000 (19664) ProactorEventTask started
Wed Jul 23 2025 05:52:19.483000 [15036] Creating endpoint instance...
Wed Jul 23 2025 05:52:19.483000 [15036] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:52:19.483000 [15036] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:52:19.487000 [4388] LM_TRACE Start Udp data dispatcher++++++Wed Jul 23 2025 05:52:19.487000 [14724] LM_TRACE Startting auido data processor

Wed Jul 23 2025 05:52:19.499000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:52:19.501000 [14804] Start command executor
Wed Jul 23 2025 05:52:19.501000 LM_TRACE [14804] cmd0267CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:52:19.501000 [14804] LM_TRACE Set user info, sid: 36962812, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 05:52:19.501000 [14804] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:52:19.501000 [14804] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:52:19.501000 [14804] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:52:19.501000 LM_TRACE [14804] cmd0267C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:52:19.501000 [14804] LM_TRACE User id: 44809717, sid: 36962812, tid: 0
Wed Jul 23 2025 05:52:19.501000 [14804] LM_TRACE Set user info, sid: 36962812, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 05:53:18.359000 [15972] LM_TRACE Process ID 18708, uid: 44809717
Wed Jul 23 2025 05:53:18.359000 LM_TRACE [14804] cmd0267D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:53:19.410000 [4388] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 05:53:19.410000 [14724] LM_TRACE Stopping auido data processor
++++++[14432] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[18836] Start log thread
[18836] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18016 -s 47.101.217.129:3080 -p 60621 -u 85960934
Wed Jul 23 2025 05:54:10.323000 [18684] LM_TRACE Start to parse argument
Wed Jul 23 2025 05:54:10.323000 [18684] LM_TRACE Parse parameter 18016 47.101.217.129:3080 60621 85960934
Wed Jul 23 2025 05:54:10.340000 [18684] LM_DEBUG Service port: 59041
Wed Jul 23 2025 05:54:10.341000 [18684] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 05:54:10.341000 (19104) AudioSocketEventTask started
++++++Wed Jul 23 2025 05:54:10.341000 (11352) ReactorEventTask started
Wed Jul 23 2025 05:54:10.428000 [18684] LM_TRACE Platform info 2130706433:60621
Wed Jul 23 2025 05:54:10.428000 [18684] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 05:54:10.439000 [11352] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 05:54:10.886000 [18684] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 05:54:10.887000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18016 -s 47.101.217.129:3080 -p 60621 -u 85960934
++++++Wed Jul 23 2025 05:54:10.971000 (16896) ProactorEventTask started
Wed Jul 23 2025 05:54:10.971000 [18684] Creating endpoint instance...
Wed Jul 23 2025 05:54:10.971000 [18684] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 05:54:10.971000 [18684] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 05:54:10.979000 [1708] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 05:54:10.979000 [15132] LM_TRACE Startting auido data processor
Wed Jul 23 2025 05:54:10.999000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 05:54:11.011000 [18916] Start command executor
Wed Jul 23 2025 05:54:11.011000 LM_TRACE [18916] cmd02BBCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:54:11.011000 [18916] LM_TRACE Set user info, sid: 37086890, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:54:11.012000 [18916] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 05:54:11.012000 [18916] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 05:54:11.012000 [18916] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 05:54:11.012000 LM_TRACE [18916] cmd02BBC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 05:54:11.012000 [18916] LM_TRACE User id: 85960934, sid: 37086890, tid: 0
Wed Jul 23 2025 05:54:11.012000 [18916] LM_TRACE Set user info, sid: 37086890, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 05:55:15.943000 [11352] LM_TRACE Process ID 18016, uid: 85960934
Wed Jul 23 2025 05:55:15.943000 LM_TRACE [18916] cmd02BBD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 05:55:20.908000 [1708] LM_TRACE Exit Udp data dispatcher
++++++[18836] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
 [10384] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:00:48.907000 [10384] LM_TRACE Parse parameter 17692 47.101.217.129:3080 52013 85960934
++++++[18188] Start log thread
[18188] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17692 -s 47.101.217.129:3080 -p 52013 -u 85960934
Wed Jul 23 2025 06:00:48.989000 [10384] LM_DEBUG Service port: 50588
Wed Jul 23 2025 06:00:48.989000 [10384] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:00:49.037000 (20436) AudioSocketEventTask started
++++++Wed Jul 23 2025 06:00:49.038000 (13776) ReactorEventTask started
Wed Jul 23 2025 06:00:49.177000 [10384] LM_TRACE Platform info 2130706433:52013
Wed Jul 23 2025 06:00:49.177000 [10384] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:00:49.255000 [13776] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:00:49.338000 [10384] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:00:49.338000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 17692 -s 47.101.217.129:3080 -p 52013 -u 85960934
++++++Wed Jul 23 2025 06:00:49.508000 (18216) ProactorEventTask started
Wed Jul 23 2025 06:00:49.508000 [10384] Creating endpoint instance...
Wed Jul 23 2025 06:00:49.508000 [10384] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:00:49.508000 [10384] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:00:49.515000 [1188] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:00:49.515000 [18228] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:00:49.532000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:00:49.534000 [992] Start command executor
Wed Jul 23 2025 06:00:49.534000 LM_TRACE [992] cmd031FCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:00:49.534000 [992] LM_TRACE Set user info, sid: 37234203, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 06:00:49.534000 [992] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:00:49.534000 [992] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:00:49.534000 [992] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:00:49.535000 LM_TRACE [992] cmd031FC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:00:49.535000 [992] LM_TRACE User id: 85960934, sid: 37234203, tid: 0
Wed Jul 23 2025 06:00:49.535000 [992] LM_TRACE Set user info, sid: 37234203, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 06:01:32.465000 [13776] LM_TRACE Process ID 17692, uid: 85960934
Wed Jul 23 2025 06:01:32.465000 LM_TRACE [992] cmd031FD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:01:39.340000 [1188] LM_TRACE Exit Udp data dispatcher++++++Wed Jul 23 2025 06:01:39.340000 [18228] LM_TRACE Stopping auido data processor

++++++[18188] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 06:02:31.149000 [12840] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:02:31.149000 [12840] LM_TRACE Parse parameter 18100 47.101.217.129:3080 57794 90042332
++++++[4104] Start log thread
[4104] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18100 -s 47.101.217.129:3080 -p 57794 -u 90042332
Wed Jul 23 2025 06:02:31.210000 [12840] LM_DEBUG Service port: 63663
Wed Jul 23 2025 06:02:31.211000 [12840] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:02:31.225000 (3684) AudioSocketEventTask started
++++++Wed Jul 23 2025 06:02:31.225000 (1508) ReactorEventTask started
Wed Jul 23 2025 06:02:31.274000 [12840] LM_TRACE Platform info 2130706433:57794
Wed Jul 23 2025 06:02:31.274000 [12840] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:02:31.307000 [1508] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:02:31.353000 [12840] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:02:31.354000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18100 -s 47.101.217.129:3080 -p 57794 -u 90042332
++++++Wed Jul 23 2025 06:02:31.440000 (17832) ProactorEventTask started
Wed Jul 23 2025 06:02:31.440000 [12840] Creating endpoint instance...
Wed Jul 23 2025 06:02:31.440000 [12840] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:02:31.440000 [12840] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:02:31.448000 [464] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:02:31.448000 [15748] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:02:31.459000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:02:31.459000 [16004] Start command executor
Wed Jul 23 2025 06:02:31.460000 LM_TRACE [16004] cmd0305CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:02:31.460000 [16004] LM_TRACE Set user info, sid: 37579562, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 06:02:31.460000 [16004] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:02:31.460000 [16004] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:02:31.460000 [16004] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:02:31.460000 LM_TRACE [16004] cmd0305C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:02:31.460000 [16004] LM_TRACE User id: 90042332, sid: 37579562, tid: 0
Wed Jul 23 2025 06:02:31.460000 [16004] LM_TRACE Set user info, sid: 37579562, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 06:03:14.775000 [1508] LM_TRACE Process ID 18100, uid: 90042332
Wed Jul 23 2025 06:03:14.775000 LM_TRACE [16004] cmd0305D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:03:21.365000 [464] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 06:03:21.365000 [15748] LM_TRACE Stopping auido data processor
++++++[4104] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[9356] Start log thread
[9356] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 4996 -s 47.101.217.129:3080 -p 49665 -u 44809717
Wed Jul 23 2025 06:04:17.970000 [17712] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:04:17.970000 [17712] LM_TRACE Parse parameter 4996 47.101.217.129:3080 49665 44809717
Wed Jul 23 2025 06:04:17.984000 [17712] LM_DEBUG Service port: 57914
Wed Jul 23 2025 06:04:17.984000 [17712] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:04:17.984000 (16264) AudioSocketEventTask started
++++++Wed Jul 23 2025 06:04:17.985000 (3984) ReactorEventTask started
Wed Jul 23 2025 06:04:18.033000 [17712] LM_TRACE Platform info 2130706433:49665
Wed Jul 23 2025 06:04:18.034000 [17712] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:04:18.096000 [3984] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:04:18.115000 [17712] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:04:18.115000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 4996 -s 47.101.217.129:3080 -p 49665 -u 44809717
++++++Wed Jul 23 2025 06:04:18.222000 (2728) ProactorEventTask started
Wed Jul 23 2025 06:04:18.222000 [17712] Creating endpoint instance...
Wed Jul 23 2025 06:04:18.222000 [17712] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:04:18.222000 [17712] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:04:18.224000 [20224] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:04:18.224000 [15820] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:04:18.236000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:04:18.236000 [18148] Start command executor
Wed Jul 23 2025 06:04:18.237000 LM_TRACE [18148] cmd02DBCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:04:18.244000 [18148] LM_TRACE Set user info, sid: 37680828, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 06:04:18.244000 [18148] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:04:18.244000 [18148] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:04:18.244000 [18148] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:04:18.244000 LM_TRACE [18148] cmd02DBC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:04:18.244000 [18148] LM_TRACE User id: 44809717, sid: 37680828, tid: 0
Wed Jul 23 2025 06:04:18.244000 [18148] LM_TRACE Set user info, sid: 37680828, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 06:05:26.622000 [3984] LM_TRACE Process ID 4996, uid: 44809717
Wed Jul 23 2025 06:05:26.622000 LM_TRACE [18148] cmd02DBD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:05:28.106000 [20224] LM_TRACE Exit Udp data dispatcher
++++++[9356] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[14332] Start log thread
[14332] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15400 -s 47.101.217.129:3080 -p 51929 -u 42020658
Wed Jul 23 2025 06:06:07.004000 [19900] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:06:07.004000 [19900] LM_TRACE Parse parameter 15400 47.101.217.129:3080 51929 42020658
Wed Jul 23 2025 06:06:07.015000 [19900] LM_DEBUG Service port: 49929
Wed Jul 23 2025 06:06:07.016000 [19900] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:06:07.016000 (18372) AudioSocketEventTask started
++++++Wed Jul 23 2025 06:06:07.016000 (13944) ReactorEventTask started
Wed Jul 23 2025 06:06:07.064000 [19900] LM_TRACE Platform info 2130706433:51929
Wed Jul 23 2025 06:06:07.064000 [19900] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:06:07.108000 [13944] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:06:07.158000 [19900] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:06:07.158000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15400 -s 47.101.217.129:3080 -p 51929 -u 42020658
++++++Wed Jul 23 2025 06:06:07.235000 (14796) ProactorEventTask started
Wed Jul 23 2025 06:06:07.235000 [19900] Creating endpoint instance...
Wed Jul 23 2025 06:06:07.235000 [19900] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:06:07.235000 [19900] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:06:07.235000 [17380] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:06:07.241000 [7628] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:06:07.253000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:06:07.254000 [14572] Start command executor
Wed Jul 23 2025 06:06:07.259000 LM_TRACE [14572] cmd02F5CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:06:07.259000 [14572] LM_TRACE Set user info, sid: 37812562, uid 42020658, tid: 0, game room id:0
Wed Jul 23 2025 06:06:07.259000 [14572] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:06:07.259000 [14572] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:06:07.259000 [14572] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:06:07.259000 LM_TRACE [14572] cmd02F5C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:06:07.260000 [14572] LM_TRACE User id: 42020658, sid: 37812562, tid: 0
Wed Jul 23 2025 06:06:07.260000 [14572] LM_TRACE Set user info, sid: 37812562, uid 42020658, tid: 0, game room id:0
Wed Jul 23 2025 06:06:51.908000 [13944] LM_TRACE Process ID 15400, uid: 42020658
Wed Jul 23 2025 06:06:51.908000 LM_TRACE [14572] cmd02F5D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:06:57.152000 [17380] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 06:06:57.152000 [7628] LM_TRACE Stopping auido data processor
++++++[14332] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 06:08:24.007000 [15340] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:08:24.007000 [15340] LM_TRACE Parse parameter 2128 47.101.217.129:3080 50431 85960934
++++++[12932] Start log thread
[12932] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 2128 -s 47.101.217.129:3080 -p 50431 -u 85960934
Wed Jul 23 2025 06:08:24.217000 [15340] LM_DEBUG Service port: 49814
Wed Jul 23 2025 06:08:24.218000 [15340] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:08:24.239000 (19788) AudioSocketEventTask started
++++++Wed Jul 23 2025 06:08:24.239000 (1188) ReactorEventTask started
Wed Jul 23 2025 06:08:24.519000 [15340] LM_TRACE Platform info 2130706433:50431
Wed Jul 23 2025 06:08:24.519000 [15340] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:08:24.520000 [1188] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:08:25.063000 [15340] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:08:25.064000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 2128 -s 47.101.217.129:3080 -p 50431 -u 85960934
++++++Wed Jul 23 2025 06:08:25.312000 (18140) ProactorEventTask started
Wed Jul 23 2025 06:08:25.313000 [15340] Creating endpoint instance...
Wed Jul 23 2025 06:08:25.313000 [15340] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:08:25.313000 [15340] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:08:25.318000 [18832] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:08:25.318000 [19896] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:08:25.343000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:08:25.344000 [13408] Start command executor
Wed Jul 23 2025 06:08:25.344000 LM_TRACE [13408] cmd0258CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:08:25.345000 [13408] LM_TRACE Set user info, sid: 37899015, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 06:08:25.345000 [13408] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:08:25.345000 [13408] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:08:25.345000 [13408] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:08:25.345000 LM_TRACE [13408] cmd0258C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:08:25.345000 [13408] LM_TRACE User id: 85960934, sid: 37899015, tid: 0
Wed Jul 23 2025 06:08:25.345000 [13408] LM_TRACE Set user info, sid: 37899015, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 06:09:07.111000 [1188] LM_TRACE Process ID 2128, uid: 85960934
Wed Jul 23 2025 06:09:07.111000 LM_TRACE [13408] cmd0258D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:09:15.081000 [18832] LM_TRACE Exit Udp data dispatcher
++++++[12932] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Wed Jul 23 2025 06:10:09.238000 [5264] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:10:09.238000 [5264] LM_TRACE Parse parameter 14764 47.101.217.129:3080 64416 90042332
++++++[8900] Start log thread
[8900] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14764 -s 47.101.217.129:3080 -p 64416 -u 90042332
Wed Jul 23 2025 06:10:09.264000 [5264] LM_DEBUG Service port: 62567
Wed Jul 23 2025 06:10:09.265000 [5264] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:10:09.292000 (7960) ReactorEventTask started
++++++Wed Jul 23 2025 06:10:09.293000 (4660) AudioSocketEventTask started
Wed Jul 23 2025 06:10:09.495000 [5264] LM_TRACE Platform info 2130706433:64416
Wed Jul 23 2025 06:10:09.496000 [5264] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:10:09.504000 [7960] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:10:09.953000 [5264] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:10:09.953000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 14764 -s 47.101.217.129:3080 -p 64416 -u 90042332
++++++Wed Jul 23 2025 06:10:10.161000 (18712) ProactorEventTask started
Wed Jul 23 2025 06:10:10.161000 [5264] Creating endpoint instance...
Wed Jul 23 2025 06:10:10.161000 [5264] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:10:10.161000 [5264] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:10:10.205000 [12404] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:10:10.205000 [16324] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:10:10.258000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:10:10.353000 [12048] Start command executor
Wed Jul 23 2025 06:10:10.353000 LM_TRACE [12048] cmd0303CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:10:10.353000 [12048] LM_TRACE Set user info, sid: 38035109, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 06:10:10.353000 [12048] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:10:10.354000 [12048] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:10:10.354000 [12048] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:10:10.354000 LM_TRACE [12048] cmd0303C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:10:10.354000 [12048] LM_TRACE User id: 90042332, sid: 38035109, tid: 0
Wed Jul 23 2025 06:10:10.354000 [12048] LM_TRACE Set user info, sid: 38035109, uid 90042332, tid: 0, game room id:0
Wed Jul 23 2025 06:11:15.323000 [7960] LM_TRACE Process ID 14764, uid: 90042332
Wed Jul 23 2025 06:11:15.324000 LM_TRACE [12048] cmd0303D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:11:19.960000 [12404] LM_TRACE Exit Udp data dispatcher
++++++Wed Jul 23 2025 06:11:19.960000 [16324] LM_TRACE Stopping auido data processor
++++++[8900] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[12812] Start log thread
[12812] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18420 -s 47.101.217.129:3080 -p 53219 -u 44809717
Wed Jul 23 2025 06:12:21.569000 [2248] LM_TRACE Start to parse argument
Wed Jul 23 2025 06:12:21.569000 [2248] LM_TRACE Parse parameter 18420 47.101.217.129:3080 53219 44809717
Wed Jul 23 2025 06:12:21.616000 [2248] LM_DEBUG Service port: 64426
Wed Jul 23 2025 06:12:21.617000 [2248] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 06:12:21.704000 (10404) AudioSocketEventTask started
++++++Wed Jul 23 2025 06:12:21.705000 (19516) ReactorEventTask started
Wed Jul 23 2025 06:12:21.891000 [2248] LM_TRACE Platform info 2130706433:53219
Wed Jul 23 2025 06:12:21.891000 [2248] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 06:12:21.901000 [19516] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 06:12:22.647000 [2248] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 06:12:22.647000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 18420 -s 47.101.217.129:3080 -p 53219 -u 44809717
++++++Wed Jul 23 2025 06:12:22.902000 (13620) ProactorEventTask started
Wed Jul 23 2025 06:12:22.902000 [2248] Creating endpoint instance...
Wed Jul 23 2025 06:12:22.902000 [2248] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 06:12:22.902000 [2248] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 06:12:22.949000 [17156] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 06:12:22.949000 [16324] LM_TRACE Startting auido data processor
Wed Jul 23 2025 06:12:23.039000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 06:12:23.101000 [80] Start command executor
Wed Jul 23 2025 06:12:23.102000 LM_TRACE [80] cmd02E8CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:12:23.102000 [80] LM_TRACE Set user info, sid: 38164296, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 06:12:23.102000 [80] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 06:12:23.102000 [80] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 06:12:23.102000 [80] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 06:12:23.102000 LM_TRACE [80] cmd02E8C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 06:12:23.102000 [80] LM_TRACE User id: 44809717, sid: 38164296, tid: 0
Wed Jul 23 2025 06:12:23.102000 [80] LM_TRACE Set user info, sid: 38164296, uid 44809717, tid: 0, game room id:0
Wed Jul 23 2025 06:13:47.374000 [19516] LM_TRACE Process ID 18420, uid: 44809717
Wed Jul 23 2025 06:13:47.374000 LM_TRACE [80] cmd02E8D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Wed Jul 23 2025 06:13:52.638000 [17156] LM_TRACE Exit Udp data dispatcher++++++Wed Jul 23 2025 06:13:52.638000 [16324] LM_TRACE Stopping auido data processor

++++++[12812] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[5628] Start log thread
[5628] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15764 -s 47.101.217.129:3080 -p 50733 -u 85960934
Wed Jul 23 2025 22:29:52.555000 [17340] LM_TRACE Start to parse argument
Wed Jul 23 2025 22:29:52.555000 [17340] LM_TRACE Parse parameter 15764 47.101.217.129:3080 50733 85960934
Wed Jul 23 2025 22:29:52.571000 [17340] LM_DEBUG Service port: 52645
Wed Jul 23 2025 22:29:52.571000 [17340] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Wed Jul 23 2025 22:29:52.572000 (7696) AudioSocketEventTask started
++++++Wed Jul 23 2025 22:29:52.572000 (13628) ReactorEventTask started
Wed Jul 23 2025 22:29:53.525000 [17340] LM_TRACE Platform info 2130706433:50733
Wed Jul 23 2025 22:29:53.526000 [17340] LM_INFO Try to initialize volume mixer control(-1, -1)
'Wed Jul 23 2025 22:29:53.607000 [13628] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Wed Jul 23 2025 22:29:53.776000 [17340] LM_ERROR failed to call waveInOpen(1)
Wed Jul 23 2025 22:29:53.777000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15764 -s 47.101.217.129:3080 -p 50733 -u 85960934
++++++Wed Jul 23 2025 22:29:54.216000 (14380) ProactorEventTask started
Wed Jul 23 2025 22:29:54.216000 [17340] Creating endpoint instance...
Wed Jul 23 2025 22:29:54.258000 [17340] LM_DEBUG Module "mod-tsx-layer" registered
Wed Jul 23 2025 22:29:54.258000 [17340] LM_DEBUG Module "mod-stateful-util" registered
++++++Wed Jul 23 2025 22:29:54.273000 [18036] LM_TRACE Start Udp data dispatcher
++++++Wed Jul 23 2025 22:29:54.274000 [14468] LM_TRACE Startting auido data processor
Wed Jul 23 2025 22:29:54.324000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Wed Jul 23 2025 22:29:54.344000 [18684] Start command executor
Wed Jul 23 2025 22:29:54.643000 LM_TRACE [18684] cmd026DCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 22:29:54.643000 [18684] LM_TRACE Set user info, sid: 9589531, uid 85960934, tid: 0, game room id:0
Wed Jul 23 2025 22:29:54.643000 [18684] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Wed Jul 23 2025 22:29:54.643000 [18684] LM_TRACE CmdSetUserInfo update process info
Wed Jul 23 2025 22:29:54.643000 [18684] LM_TRACE CmdSetUserInfo execute successfully
Wed Jul 23 2025 22:29:54.643000 LM_TRACE [18684] cmd026DC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Wed Jul 23 2025 22:29:54.643000 [18684] LM_TRACE User id: 85960934, sid: 9589531, tid: 0
Wed Jul 23 2025 22:29:54.643000 [18684] LM_TRACE Set user info, sid: 9589531, uid 85960934, tid: 0, game room id:0
Thu Jul 24 2025 03:53:15.356000 [13628] LM_TRACE Process ID 15764, uid: 85960934
Thu Jul 24 2025 03:53:15.393000 LM_TRACE [18684] cmd026DD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Thu Jul 24 2025 03:53:24.140000 [18036] LM_TRACE Exit Udp data dispatcher
++++++[5628] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[11968] Start log thread
[11968] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 12704 -s 47.101.217.129:3080 -p 55568 -u 85960934
Thu Jul 24 2025 04:03:34.327000 [9660] LM_TRACE Start to parse argument
Thu Jul 24 2025 04:03:34.408000 [9660] LM_TRACE Parse parameter 12704 47.101.217.129:3080 55568 85960934
Thu Jul 24 2025 04:03:35.229000 [9660] LM_DEBUG Service port: 64007
Thu Jul 24 2025 04:03:35.231000 [9660] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Thu Jul 24 2025 04:03:35.273000 (12108) AudioSocketEventTask started
++++++Thu Jul 24 2025 04:03:35.274000 (1936) ReactorEventTask started
Thu Jul 24 2025 04:03:36.539000 [9660] LM_TRACE Platform info 2130706433:55568
Thu Jul 24 2025 04:03:36.684000 [9660] LM_INFO Try to initialize volume mixer control(-1, -1)
Thu Jul 24 2025 04:03:36.957000 [9660] LM_ERROR failed to call waveInOpen(1)
Thu Jul 24 2025 04:03:36.957000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 12704 -s 47.101.217.129:3080 -p 55568 -u 85960934
++++++Thu Jul 24 2025 04:03:37.010000 (14060) ProactorEventTask started
Thu Jul 24 2025 04:03:37.069000 [9660] Creating endpoint instance...
Thu Jul 24 2025 04:03:37.171000 [9660] LM_DEBUG Module "mod-tsx-layer" registered
Thu Jul 24 2025 04:03:37.171000 [9660] LM_DEBUG Module "mod-stateful-util" registered
++++++Thu Jul 24 2025 04:03:37.172000 [11560] LM_TRACE Start Udp data dispatcher
++++++Thu Jul 24 2025 04:03:37.292000 [9100] LM_TRACE Startting auido data processor
Thu Jul 24 2025 04:03:37.302000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Thu Jul 24 2025 04:03:37.303000 [6544] Start command executor
'Thu Jul 24 2025 04:03:37.874000 [1936] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Thu Jul 24 2025 04:03:37.874000 LM_TRACE [6544] cmd02EBCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Thu Jul 24 2025 04:03:37.874000 [6544] LM_TRACE Set user info, sid: 327656, uid 85960934, tid: 0, game room id:0
Thu Jul 24 2025 04:03:37.874000 [6544] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Thu Jul 24 2025 04:03:37.874000 [6544] LM_TRACE CmdSetUserInfo update process info
Thu Jul 24 2025 04:03:37.874000 [6544] LM_TRACE CmdSetUserInfo execute successfully
Thu Jul 24 2025 04:03:37.980000 LM_TRACE [6544] cmd02EBC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Thu Jul 24 2025 04:03:37.980000 [6544] LM_TRACE User id: 85960934, sid: 327656, tid: 0
Thu Jul 24 2025 04:03:37.980000 [6544] LM_TRACE Set user info, sid: 327656, uid 85960934, tid: 0, game room id:0
Unknown command id 4194
Unknown command id 4194
Unknown command id 4194
Thu Jul 24 2025 06:12:28.144000 [1936] LM_TRACE Process ID 12704, uid: 85960934
Thu Jul 24 2025 06:12:28.160000 LM_TRACE [6544] cmd02EBD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Thu Jul 24 2025 06:12:28.626000 [11560] LM_TRACE Exit Udp data dispatcher
++++++[11968] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Sat Jul 26 2025 04:09:24.959000 [13628] LM_TRACE Start to parse argument
Sat Jul 26 2025 04:09:24.959000 [13628] LM_TRACE Parse parameter 15092 47.101.217.129:3080 57974 85960934
++++++[15476] Start log thread
[15476] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15092 -s 47.101.217.129:3080 -p 57974 -u 85960934
Sat Jul 26 2025 04:09:25.042000 [13628] LM_DEBUG Service port: 56380
Sat Jul 26 2025 04:09:25.042000 [13628] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sat Jul 26 2025 04:09:25.112000 (2296) AudioSocketEventTask started
++++++Sat Jul 26 2025 04:09:25.112000 (10964) ReactorEventTask started
Sat Jul 26 2025 04:09:27.126000 [13628] LM_TRACE Platform info 2130706433:57974
Sat Jul 26 2025 04:09:27.149000 [13628] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 26 2025 04:09:27.150000 [10964] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 26 2025 04:09:36.120000 [13628] LM_ERROR failed to call waveInOpen(1)
Sat Jul 26 2025 04:09:36.120000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 15092 -s 47.101.217.129:3080 -p 57974 -u 85960934
++++++Sat Jul 26 2025 04:09:38.836000 (2204) ProactorEventTask started
Sat Jul 26 2025 04:09:38.837000 [13628] Creating endpoint instance...
Sat Jul 26 2025 04:09:38.837000 [13628] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 26 2025 04:09:38.837000 [13628] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 26 2025 04:09:39.187000 [17452] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 26 2025 04:09:39.189000 [17456] LM_TRACE Startting auido data processor
Sat Jul 26 2025 04:09:48.429000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 26 2025 04:09:48.434000 [17452] LM_TRACE Exit Udp data dispatcher
++++++Sat Jul 26 2025 04:09:49.052000 [18120] Start command executor
Sat Jul 26 2025 04:09:49.053000 LM_TRACE [18120] cmd02C5CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 04:09:49.053000 [18120] LM_TRACE Set user info, sid: 21113187, uid 85960934, tid: 0, game room id:0
Sat Jul 26 2025 04:09:49.053000 [18120] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 26 2025 04:09:49.053000 [18120] LM_TRACE CmdSetUserInfo update process info
Sat Jul 26 2025 04:09:49.053000 [18120] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 26 2025 04:09:49.053000 LM_TRACE [18120] cmd02C5C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 04:09:49.053000 [18120] LM_TRACE User id: 85960934, sid: 21113187, tid: 0
Sat Jul 26 2025 04:09:49.053000 [18120] LM_TRACE Set user info, sid: 21113187, uid 85960934, tid: 0, game room id:0
++++++Sat Jul 26 2025 04:09:49.053000 (2204) ProactorEventTask finished
++++++[15476] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[14352] Start log thread
[14352] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 19220 -s 47.101.217.129:3080 -p 52840 -u 85960934
Sat Jul 26 2025 13:49:38.538000 [17704] LM_TRACE Start to parse argument
Sat Jul 26 2025 13:49:38.557000 [17704] LM_TRACE Parse parameter 19220 47.101.217.129:3080 52840 85960934
Sat Jul 26 2025 13:49:38.574000 [17704] LM_DEBUG Service port: 53616
Sat Jul 26 2025 13:49:38.574000 [17704] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sat Jul 26 2025 13:49:38.576000 (14572) AudioSocketEventTask started
++++++Sat Jul 26 2025 13:49:38.576000 (9360) ReactorEventTask started
Sat Jul 26 2025 13:49:39.542000 [17704] LM_TRACE Platform info 2130706433:52840
Sat Jul 26 2025 13:49:39.542000 [17704] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 26 2025 13:49:39.543000 [9360] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 26 2025 13:49:39.902000 [17704] LM_ERROR failed to call waveInOpen(1)
Sat Jul 26 2025 13:49:39.902000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 19220 -s 47.101.217.129:3080 -p 52840 -u 85960934
++++++Sat Jul 26 2025 13:49:40.244000 (16152) ProactorEventTask started
Sat Jul 26 2025 13:49:40.245000 [17704] Creating endpoint instance...
Sat Jul 26 2025 13:49:40.245000 [17704] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 26 2025 13:49:40.245000 [17704] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 26 2025 13:49:40.268000 [18204] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 26 2025 13:49:40.270000 [9356] LM_TRACE Startting auido data processor
Sat Jul 26 2025 13:49:40.308000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 26 2025 13:49:40.313000 [17432] Start command executor
Sat Jul 26 2025 13:49:40.313000 LM_TRACE [17432] cmd029FCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 13:49:40.313000 [17432] LM_TRACE Set user info, sid: 13646046, uid 85960934, tid: 0, game room id:0
Sat Jul 26 2025 13:49:40.313000 [17432] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 26 2025 13:49:40.313000 [17432] LM_TRACE CmdSetUserInfo update process info
Sat Jul 26 2025 13:49:40.315000 [17432] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 26 2025 13:49:40.315000 LM_TRACE [17432] cmd029FC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 13:49:40.315000 [17432] LM_TRACE User id: 85960934, sid: 13646046, tid: 0
Sat Jul 26 2025 13:49:40.315000 [17432] LM_TRACE Set user info, sid: 13646046, uid 85960934, tid: 0, game room id:0
Sat Jul 26 2025 14:55:36.547000 [9360] LM_TRACE Process ID 19220, uid: 85960934
Sat Jul 26 2025 14:55:36.577000 LM_TRACE [17432] cmd029FD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sat Jul 26 2025 14:55:39.943000 [18204] LM_TRACE Exit Udp data dispatcher
++++++[14352] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[2164] Start log thread
[2164] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 11424 -s 47.101.217.129:3080 -p 61625 -u 90042332
Sat Jul 26 2025 21:48:04.643000 [15260] LM_TRACE Start to parse argument
Sat Jul 26 2025 21:48:04.679000 [15260] LM_TRACE Parse parameter 11424 47.101.217.129:3080 61625 90042332
Sat Jul 26 2025 21:48:04.919000 [15260] LM_DEBUG Service port: 59686
++++++Sat Jul 26 2025 21:48:04.919000 (15048) AudioSocketEventTask started
++++++Sat Jul 26 2025 21:48:04.920000 (20104) ReactorEventTask started
Sat Jul 26 2025 21:48:04.973000 [15260] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Sat Jul 26 2025 21:48:05.524000 [15260] LM_TRACE Platform info 2130706433:61625
Sat Jul 26 2025 21:48:05.524000 [15260] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 26 2025 21:48:05.525000 [20104] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 26 2025 21:48:05.791000 [15260] LM_ERROR failed to call waveInOpen(1)
Sat Jul 26 2025 21:48:05.792000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 11424 -s 47.101.217.129:3080 -p 61625 -u 90042332
++++++Sat Jul 26 2025 21:48:05.900000 (20008) ProactorEventTask started
Sat Jul 26 2025 21:48:05.900000 [15260] Creating endpoint instance...
Sat Jul 26 2025 21:48:06.019000 [15260] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 26 2025 21:48:06.019000 [15260] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 26 2025 21:48:06.019000 [19220] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 26 2025 21:48:06.021000 [17580] LM_TRACE Startting auido data processor
Sat Jul 26 2025 21:48:06.033000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 26 2025 21:48:06.034000 [19692] Start command executor
Sat Jul 26 2025 21:48:06.034000 LM_TRACE [19692] cmd02F0CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 21:48:06.034000 [19692] LM_TRACE Set user info, sid: 17680593, uid 90042332, tid: 0, game room id:0
Sat Jul 26 2025 21:48:06.034000 [19692] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 26 2025 21:48:06.034000 [19692] LM_TRACE CmdSetUserInfo update process info
Sat Jul 26 2025 21:48:06.034000 [19692] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 26 2025 21:48:06.034000 LM_TRACE [19692] cmd02F0C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 21:48:06.034000 [19692] LM_TRACE User id: 90042332, sid: 17680593, tid: 0
Sat Jul 26 2025 21:48:06.034000 [19692] LM_TRACE Set user info, sid: 17680593, uid 90042332, tid: 0, game room id:0
Sat Jul 26 2025 22:02:18.231000 [20104] LM_TRACE Process ID 11424, uid: 90042332
Sat Jul 26 2025 22:02:18.231000 LM_TRACE [19692] cmd02F0D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sat Jul 26 2025 22:02:25.801000 [19220] LM_TRACE Exit Udp data dispatcher
++++++[2164] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Sat Jul 26 2025 22:03:38.110000 [13640] LM_TRACE Start to parse argument
Sat Jul 26 2025 22:03:38.110000 [13640] LM_TRACE Parse parameter 3672 47.101.217.129:3080 52433 85960934
++++++[16020] Start log thread
[16020] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 3672 -s 47.101.217.129:3080 -p 52433 -u 85960934
Sat Jul 26 2025 22:03:38.151000 [13640] LM_DEBUG Service port: 62645
Sat Jul 26 2025 22:03:38.152000 [13640] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sat Jul 26 2025 22:03:38.186000 (20312) AudioSocketEventTask started
++++++Sat Jul 26 2025 22:03:38.187000 (20236) ReactorEventTask started
Sat Jul 26 2025 22:03:38.265000 [13640] LM_TRACE Platform info 2130706433:52433
Sat Jul 26 2025 22:03:38.265000 [13640] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 26 2025 22:03:38.282000 [20236] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 26 2025 22:03:38.415000 [13640] LM_ERROR failed to call waveInOpen(1)
Sat Jul 26 2025 22:03:38.415000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 3672 -s 47.101.217.129:3080 -p 52433 -u 85960934
++++++Sat Jul 26 2025 22:03:38.498000 (20252) ProactorEventTask started
Sat Jul 26 2025 22:03:38.498000 [13640] Creating endpoint instance...
Sat Jul 26 2025 22:03:38.498000 [13640] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 26 2025 22:03:38.498000 [13640] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 26 2025 22:03:38.504000 [4016] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 26 2025 22:03:38.504000 [17004] LM_TRACE Startting auido data processor
Sat Jul 26 2025 22:03:38.515000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 26 2025 22:03:38.516000 [14080] Start command executor
Sat Jul 26 2025 22:03:38.517000 LM_TRACE [14080] cmd02FFCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 22:03:38.517000 [14080] LM_TRACE Set user info, sid: 43276265, uid 85960934, tid: 0, game room id:0
Sat Jul 26 2025 22:03:38.517000 [14080] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 26 2025 22:03:38.517000 [14080] LM_TRACE CmdSetUserInfo update process info
Sat Jul 26 2025 22:03:38.517000 [14080] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 26 2025 22:03:38.517000 LM_TRACE [14080] cmd02FFC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 22:03:38.517000 [14080] LM_TRACE User id: 85960934, sid: 43276265, tid: 0
Sat Jul 26 2025 22:03:38.517000 [14080] LM_TRACE Set user info, sid: 43276265, uid 85960934, tid: 0, game room id:0
Sat Jul 26 2025 22:27:37.012000 [20236] LM_TRACE Process ID 3672, uid: 85960934
Sat Jul 26 2025 22:27:37.033000 LM_TRACE [14080] cmd02FFD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sat Jul 26 2025 22:27:38.433000 [4016] LM_TRACE Exit Udp data dispatcher
++++++Sat Jul 26 2025 22:27:38.433000 [17004] LM_TRACE Stopping auido data processor
++++++[16020] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Sat Jul 26 2025 22:28:29.534000 [5252] LM_TRACE Start to parse argument
Sat Jul 26 2025 22:28:29.534000 [5252] LM_TRACE Parse parameter 19796 47.101.217.129:3080 65048 90042332
++++++[11652] Start log thread
[11652] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 19796 -s 47.101.217.129:3080 -p 65048 -u 90042332
Sat Jul 26 2025 22:28:29.713000 [5252] LM_DEBUG Service port: 51177
Sat Jul 26 2025 22:28:29.714000 [5252] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sat Jul 26 2025 22:28:29.803000 (3272) AudioSocketEventTask started
++++++Sat Jul 26 2025 22:28:29.803000 (4380) ReactorEventTask started
Sat Jul 26 2025 22:28:30.951000 [5252] LM_TRACE Platform info 2130706433:65048
Sat Jul 26 2025 22:28:30.975000 [5252] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sat Jul 26 2025 22:28:30.976000 [4380] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sat Jul 26 2025 22:28:31.198000 [5252] LM_ERROR failed to call waveInOpen(1)
Sat Jul 26 2025 22:28:31.198000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 19796 -s 47.101.217.129:3080 -p 65048 -u 90042332
++++++Sat Jul 26 2025 22:28:31.300000 (144) ProactorEventTask started
Sat Jul 26 2025 22:28:31.300000 [5252] Creating endpoint instance...
Sat Jul 26 2025 22:28:31.300000 [5252] LM_DEBUG Module "mod-tsx-layer" registered
Sat Jul 26 2025 22:28:31.300000 [5252] LM_DEBUG Module "mod-stateful-util" registered
++++++Sat Jul 26 2025 22:28:31.302000 [20336] LM_TRACE Start Udp data dispatcher
++++++Sat Jul 26 2025 22:28:31.302000 [9352] LM_TRACE Startting auido data processor
Sat Jul 26 2025 22:28:31.312000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sat Jul 26 2025 22:28:31.313000 [4244] Start command executor
Sat Jul 26 2025 22:28:31.314000 LM_TRACE [4244] cmd0316CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 22:28:31.314000 [4244] LM_TRACE Set user info, sid: 44799156, uid 90042332, tid: 0, game room id:0
Sat Jul 26 2025 22:28:31.314000 [4244] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sat Jul 26 2025 22:28:31.314000 [4244] LM_TRACE CmdSetUserInfo update process info
Sat Jul 26 2025 22:28:31.314000 [4244] LM_TRACE CmdSetUserInfo execute successfully
Sat Jul 26 2025 22:28:31.314000 LM_TRACE [4244] cmd0316C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sat Jul 26 2025 22:28:31.314000 [4244] LM_TRACE User id: 90042332, sid: 44799156, tid: 0
Sat Jul 26 2025 22:28:31.314000 [4244] LM_TRACE Set user info, sid: 44799156, uid 90042332, tid: 0, game room id:0
Sat Jul 26 2025 22:45:02.734000 [4380] LM_TRACE Process ID 19796, uid: 90042332
Sat Jul 26 2025 22:45:02.734000 LM_TRACE [4244] cmd0316D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sat Jul 26 2025 22:45:11.208000 [20336] LM_TRACE Exit Udp data dispatcher++++++Sat Jul 26 2025 22:45:11.208000 [9352] LM_TRACE Stopping auido data processor

++++++[11652] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[2176] Start log thread
[2176] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 12048 -s 47.101.217.129:3080 -p 62317 -u 85960934
Sun Jul 27 2025 10:10:19.689000 [16940] LM_TRACE Start to parse argument
Sun Jul 27 2025 10:10:19.689000 [16940] LM_TRACE Parse parameter 12048 47.101.217.129:3080 62317 85960934
Sun Jul 27 2025 10:10:19.722000 [16940] LM_DEBUG Service port: 59155
++++++Sun Jul 27 2025 10:10:19.727000 (6292) AudioSocketEventTask started
++++++Sun Jul 27 2025 10:10:19.727000 (11032) ReactorEventTask started
Sun Jul 27 2025 10:10:19.753000 [16940] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Sun Jul 27 2025 10:10:21.007000 [16940] LM_TRACE Platform info 2130706433:62317
Sun Jul 27 2025 10:10:21.007000 [16940] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sun Jul 27 2025 10:10:21.008000 [11032] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sun Jul 27 2025 10:10:24.074000 [16940] LM_ERROR failed to call waveInOpen(1)
Sun Jul 27 2025 10:10:24.074000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 12048 -s 47.101.217.129:3080 -p 62317 -u 85960934
++++++Sun Jul 27 2025 10:10:24.826000 (16868) ProactorEventTask started
Sun Jul 27 2025 10:10:24.826000 [16940] Creating endpoint instance...
Sun Jul 27 2025 10:10:24.826000 [16940] LM_DEBUG Module "mod-tsx-layer" registered
Sun Jul 27 2025 10:10:24.826000 [16940] LM_DEBUG Module "mod-stateful-util" registered
++++++Sun Jul 27 2025 10:10:24.875000 [6452] LM_TRACE Start Udp data dispatcher
++++++Sun Jul 27 2025 10:10:24.875000 [7400] LM_TRACE Startting auido data processor
Sun Jul 27 2025 10:10:24.921000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sun Jul 27 2025 10:10:24.941000 [15004] Start command executor
Sun Jul 27 2025 10:10:24.941000 LM_TRACE [15004] cmd0240CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sun Jul 27 2025 10:10:24.941000 [15004] LM_TRACE Set user info, sid: 34392078, uid 85960934, tid: 0, game room id:0
Sun Jul 27 2025 10:10:24.941000 [15004] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sun Jul 27 2025 10:10:24.941000 [15004] LM_TRACE CmdSetUserInfo update process info
Sun Jul 27 2025 10:10:24.941000 [15004] LM_TRACE CmdSetUserInfo execute successfully
Sun Jul 27 2025 10:10:24.941000 LM_TRACE [15004] cmd0240C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sun Jul 27 2025 10:10:24.941000 [15004] LM_TRACE User id: 85960934, sid: 34392078, tid: 0
Sun Jul 27 2025 10:10:24.941000 [15004] LM_TRACE Set user info, sid: 34392078, uid 85960934, tid: 0, game room id:0
******** System Locale: Chinese (Simplified)_China.936
Sun Jul 27 2025 21:24:53.295000 [16976] LM_TRACE Start to parse argument
Sun Jul 27 2025 21:24:53.344000 [16976] LM_TRACE Parse parameter 13596 47.101.217.129:3080 55011 85960934
++++++[14012] Start log thread
[14012] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 13596 -s 47.101.217.129:3080 -p 55011 -u 85960934
Sun Jul 27 2025 21:24:53.556000 [16976] LM_DEBUG Service port: 49665
Sun Jul 27 2025 21:24:53.556000 [16976] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Sun Jul 27 2025 21:24:53.583000 (5748) AudioSocketEventTask started
++++++Sun Jul 27 2025 21:24:53.583000 (5148) ReactorEventTask started
Sun Jul 27 2025 21:24:54.996000 [16976] LM_TRACE Platform info 2130706433:55011
'Sun Jul 27 2025 21:24:54.997000 [5148] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sun Jul 27 2025 21:24:54.998000 [16976] LM_INFO Try to initialize volume mixer control(-1, -1)
Sun Jul 27 2025 21:24:56.460000 [16976] LM_ERROR failed to call waveInOpen(1)
Sun Jul 27 2025 21:24:56.460000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 13596 -s 47.101.217.129:3080 -p 55011 -u 85960934
++++++Sun Jul 27 2025 21:24:56.761000 (18096) ProactorEventTask started
Sun Jul 27 2025 21:24:56.871000 [16976] Creating endpoint instance...
Sun Jul 27 2025 21:24:57.007000 [16976] LM_DEBUG Module "mod-tsx-layer" registered
Sun Jul 27 2025 21:24:57.007000 [16976] LM_DEBUG Module "mod-stateful-util" registered
++++++Sun Jul 27 2025 21:24:57.008000 [16964] LM_TRACE Start Udp data dispatcher
++++++Sun Jul 27 2025 21:24:57.080000 [15388] LM_TRACE Startting auido data processor
Sun Jul 27 2025 21:24:57.161000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sun Jul 27 2025 21:24:57.162000 [13288] Start command executor
Sun Jul 27 2025 21:24:57.188000 LM_TRACE [13288] cmd0284CE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sun Jul 27 2025 21:24:57.188000 [13288] LM_TRACE Set user info, sid: 34269781, uid 85960934, tid: 0, game room id:0
Sun Jul 27 2025 21:24:57.188000 [13288] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sun Jul 27 2025 21:24:57.188000 [13288] LM_TRACE CmdSetUserInfo update process info
Sun Jul 27 2025 21:24:57.188000 [13288] LM_TRACE CmdSetUserInfo execute successfully
Sun Jul 27 2025 21:24:57.188000 LM_TRACE [13288] cmd0284C980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sun Jul 27 2025 21:24:57.188000 [13288] LM_TRACE User id: 85960934, sid: 34269781, tid: 0
Sun Jul 27 2025 21:24:57.188000 [13288] LM_TRACE Set user info, sid: 34269781, uid 85960934, tid: 0, game room id:0
Sun Jul 27 2025 23:24:03.299000 [5148] LM_TRACE Process ID 13596, uid: 85960934
Sun Jul 27 2025 23:24:03.353000 LM_TRACE [13288] cmd0284D2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Sun Jul 27 2025 23:24:06.766000 [16964] LM_TRACE Exit Udp data dispatcher
++++++[14012] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
++++++[16164] Start log thread
[16164] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 4248 -s 47.101.217.129:3080 -p 56182 -u 124735627
Sun Jul 27 2025 23:55:11.479000 [15120] LM_TRACE Start to parse argument
Sun Jul 27 2025 23:55:11.479000 [15120] LM_TRACE Parse parameter 4248 47.101.217.129:3080 56182 124735627
Sun Jul 27 2025 23:55:11.495000 [15120] LM_DEBUG Service port: 59077
++++++Sun Jul 27 2025 23:55:11.495000 (1908) AudioSocketEventTask started
++++++Sun Jul 27 2025 23:55:11.495000 (13244) ReactorEventTask started
Sun Jul 27 2025 23:55:11.496000 [15120] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
Sun Jul 27 2025 23:55:11.651000 [15120] LM_TRACE Platform info 2130706433:56182
Sun Jul 27 2025 23:55:11.651000 [15120] LM_INFO Try to initialize volume mixer control(-1, -1)
'Sun Jul 27 2025 23:55:11.722000 [13244] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Sun Jul 27 2025 23:55:14.235000 [15120] LM_ERROR failed to call waveInOpen(1)
Sun Jul 27 2025 23:55:14.235000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 4248 -s 47.101.217.129:3080 -p 56182 -u 124735627
++++++Sun Jul 27 2025 23:55:14.689000 (16456) ProactorEventTask started
Sun Jul 27 2025 23:55:14.727000 [15120] Creating endpoint instance...
Sun Jul 27 2025 23:55:14.861000 [15120] LM_DEBUG Module "mod-tsx-layer" registered
Sun Jul 27 2025 23:55:14.861000 [15120] LM_DEBUG Module "mod-stateful-util" registered
++++++Sun Jul 27 2025 23:55:15.379000 [13872] LM_TRACE Start Udp data dispatcher
++++++Sun Jul 27 2025 23:55:15.529000 [3724] LM_TRACE Startting auido data processor
Sun Jul 27 2025 23:55:15.750000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Sun Jul 27 2025 23:55:15.756000 [20328] Start command executor
Sun Jul 27 2025 23:55:15.756000 LM_TRACE [20328] cmd009ACE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sun Jul 27 2025 23:55:15.756000 [20328] LM_TRACE Set user info, sid: 41613062, uid 124735627, tid: 0, game room id:0
Sun Jul 27 2025 23:55:15.756000 [20328] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Sun Jul 27 2025 23:55:15.756000 [20328] LM_TRACE CmdSetUserInfo update process info
Sun Jul 27 2025 23:55:15.756000 [20328] LM_TRACE CmdSetUserInfo execute successfully
Sun Jul 27 2025 23:55:15.756000 LM_TRACE [20328] cmd009AC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Sun Jul 27 2025 23:55:15.756000 [20328] LM_TRACE User id: 124735627, sid: 41613062, tid: 0
Sun Jul 27 2025 23:55:15.757000 [20328] LM_TRACE Set user info, sid: 41613062, uid 124735627, tid: 0, game room id:0
Mon Jul 28 2025 01:19:02.530000 [13244] LM_TRACE Process ID 4248, uid: 124735627
Mon Jul 28 2025 01:19:02.555000 LM_TRACE [20328] cmd009AD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Mon Jul 28 2025 01:19:04.397000 [13872] LM_TRACE Exit Udp data dispatcher
++++++[16164] Exit log thread
******** System Locale: Chinese (Simplified)_China.936
Mon Jul 28 2025 01:42:38.264000 [18976] LM_TRACE Start to parse argument
Mon Jul 28 2025 01:42:38.264000 [18976] LM_TRACE Parse parameter 10988 47.101.217.129:3080 60928 85960934
++++++[15676] Start log thread
[15676] "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 10988 -s 47.101.217.129:3080 -p 60928 -u 85960934
Mon Jul 28 2025 01:42:38.310000 [18976] LM_DEBUG Service port: 52955
Mon Jul 28 2025 01:42:38.310000 [18976] LM_TRACE Try to change audio device
From -1, -1 to -1, -1
++++++Mon Jul 28 2025 01:42:38.346000 (13292) AudioSocketEventTask started
++++++Mon Jul 28 2025 01:42:38.346000 (18360) ReactorEventTask started
Mon Jul 28 2025 01:42:38.711000 [18976] LM_TRACE Platform info 2130706433:60928
Mon Jul 28 2025 01:42:38.711000 [18976] LM_INFO Try to initialize volume mixer control(-1, -1)
'Mon Jul 28 2025 01:42:38.722000 [18360] LM_DEBUG SetUserInfo get new struct, game room id (0), cert size (104)
Mon Jul 28 2025 01:42:38.975000 [18976] LM_ERROR failed to call waveInOpen(1)
Mon Jul 28 2025 01:42:38.976000 LM_TRACE 7fMedia run with parameter "G:\YMSJ\qfyxzdh\7fgame\Service\Media\7fMedia.exe" -i 10988 -s 47.101.217.129:3080 -p 60928 -u 85960934
++++++Mon Jul 28 2025 01:42:39.059000 (17224) ProactorEventTask started
Mon Jul 28 2025 01:42:39.059000 [18976] Creating endpoint instance...
Mon Jul 28 2025 01:42:39.059000 [18976] LM_DEBUG Module "mod-tsx-layer" registered
Mon Jul 28 2025 01:42:39.059000 [18976] LM_DEBUG Module "mod-stateful-util" registered
++++++Mon Jul 28 2025 01:42:39.060000 [17036] LM_TRACE Start Udp data dispatcher
++++++Mon Jul 28 2025 01:42:39.061000 [17048] LM_TRACE Startting auido data processor
Mon Jul 28 2025 01:42:39.071000 Initialize 7fMedia product ver: 1.0.0.1, file ver: 1.0.411.1617
++++++Mon Jul 28 2025 01:42:39.072000 [9692] Start command executor
Mon Jul 28 2025 01:42:39.072000 LM_TRACE [9692] cmd033CCE30 Execute CMD_CP_PLAT2SERVICE_LOGININFO from CMD_CROSSPROCESS_7FSERVICE_PARAM
Mon Jul 28 2025 01:42:39.072000 [9692] LM_TRACE Set user info, sid: 48514937, uid 85960934, tid: 0, game room id:0
Mon Jul 28 2025 01:42:39.072000 [9692] LM_TRACE CmdSetUserInfo set user info
Caller process count 1
Mon Jul 28 2025 01:42:39.072000 [9692] LM_TRACE CmdSetUserInfo update process info
Mon Jul 28 2025 01:42:39.072000 [9692] LM_TRACE CmdSetUserInfo execute successfully
Mon Jul 28 2025 01:42:39.072000 LM_TRACE [9692] cmd033CC980 Execute CMD_CP_PLAT2SERVICE_LOGINSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Mon Jul 28 2025 01:42:39.073000 [9692] LM_TRACE User id: 85960934, sid: 48514937, tid: 0
Mon Jul 28 2025 01:42:39.073000 [9692] LM_TRACE Set user info, sid: 48514937, uid 85960934, tid: 0, game room id:0
Mon Jul 28 2025 01:43:24.068000 [18360] LM_TRACE Process ID 10988, uid: 85960934
Mon Jul 28 2025 01:43:24.068000 LM_TRACE [9692] cmd033CD2A0 Execute CMD_CP_PLAT2SERVICE_LOGOUTSERVER from CMD_CROSSPROCESS_7FSERVICE_PARAM
Caller process count 0
++++++Mon Jul 28 2025 01:43:28.973000 [17036] LM_TRACE Exit Udp data dispatcher
++++++Mon Jul 28 2025 01:43:28.973000 [17048] LM_TRACE Stopping auido data processor
++++++[15676] Exit log thread
