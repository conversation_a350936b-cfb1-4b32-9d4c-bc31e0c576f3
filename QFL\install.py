#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境安装脚本

开发者: @ConceptualGod
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """安装依赖包"""
    print("正在安装Python依赖包...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("错误: requirements.txt 文件不存在 - By @ConceptualGod")
        return False
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])

        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])

        print("依赖包安装完成! - By @ConceptualGod")
        return True

    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e} - By @ConceptualGod")
        print("注意: 某些包可能已经安装或版本不兼容，程序将尝试继续运行")
        return True  # 改为True，允许程序继续运行

def create_directories():
    """创建必要的目录"""
    print("创建必要的目录...")
    
    directories = [
        "logs",
        "screenshots",
        "backup",
        "data"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"创建目录: {directory}")

def check_templates():
    """检查模板文件"""
    print("检查模板文件...")
    
    templates_dir = Path("templates")
    if not templates_dir.exists():
        print("警告: templates 目录不存在 - By @ConceptualGod")
        return False
    
    required_subdirs = ["countries", "hero", "pick", "task"]
    missing_dirs = []
    
    for subdir in required_subdirs:
        subdir_path = templates_dir / subdir
        if not subdir_path.exists():
            missing_dirs.append(subdir)
    
    if missing_dirs:
        print(f"警告: 缺少模板子目录: {missing_dirs} - By @ConceptualGod")
        return False
    
    print("模板文件检查完成 - By @ConceptualGod")
    return True

def check_config_files():
    """检查配置文件"""
    print("检查配置文件...")
    
    config_files = [
        "coordinates_1.json",
        "coordinates_2.json", 
        "coordinates_3.json",
        "exit.json",
        "账号表.xlsx"
    ]
    
    missing_files = []
    for config_file in config_files:
        if not Path(config_file).exists():
            missing_files.append(config_file)
    
    if missing_files:
        print(f"警告: 缺少配置文件: {missing_files} - By @ConceptualGod")
        return False
    
    print("配置文件检查完成 - By @ConceptualGod")
    return True

def main():
    """主安装流程"""
    print("=" * 50)
    print("起凡自动化脚本环境安装")
    print("By @ConceptualGod")
    print("=" * 50)
    
    # 1. 安装依赖包
    if not install_requirements():
        print("安装失败! - By @ConceptualGod")
        return
    
    # 2. 创建目录
    create_directories()
    
    # 3. 检查模板文件
    check_templates()
    
    # 4. 检查配置文件
    check_config_files()
    
    print("\n" + "=" * 50)
    print("环境安装完成! - By @ConceptualGod")
    print("=" * 50)
    print("\n使用方法:")
    print("运行GUI程序: python gui_main.py")
    print("\n注意事项:")
    print("1. 请确保templates目录下有完整的模板图片")
    print("2. 支持导入CSV/Excel格式的账号文件")
    print("3. 只需要账号和密码两列数据")
    print("4. 自动去重，跳过重复账号")
    print("5. 所有操作和日志都在GUI界面中显示")

if __name__ == "__main__":
    main()
